import { AxiosInstance } from '.'
import { notify, simplifyBackendError } from '../../shared/helpers/util'

interface I_CreateCompany {
  companyName: string
  description: string
}

interface I_UpsertCompanyCommision {
  _id: string

  repairBonus: number
  selfLead: number
  benchmarks: Array<any>
  createdBy: string
}

interface I_UpsertCompanySettings {
  _id?: string

  defaultPO: string
  weekStartDay: string
  weekEndDays: string[]
  timeZone: string
  dailyOH: number
  dailyLabor: number
  dailyProfit: number
  salesComm?: number
  commission: number
  financeMod: number
  actRevGoal: number
  matMarkup: number
  repairMarkup?: number
  repairMinimum?: number
  travelFee: number
  laborWaste: number
  manHourRate: number
  plywoodRate: number
  weekendBonus: number
  ttlBurden: number
  insWorkersComp: number
  insUnemployment: number
  ssMedicare: number
  salesTaxWA?: number
  salesTaxID?: number
  createdBy: string
}
interface I_UpdateCompanyMemberInfo {
  name: string
  memberId: string
  hireDate?: any
  managerId: string
  departmentId: string
  subContractorId?: string
}
interface I_GetCompensationInfo {
  memberId: string
}

interface I_CreateReferrer {
  name: string
}

interface I_MutateReferrer {
  id: string
}
interface I_GetDirectReport {
  memberId: string
}

interface I_DeleteMember {
  memberId: string
}
interface I_TerminateMember {
  memberId: string
  message: string
  terminateDate: string
}
interface I_RestoreMember {
  memberId: string
}
export const updateSectionOrder = async (data: any, contractId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(`/company/order-section/contractId/${contractId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const updateSection = async (data: any, contractId: string, sectionId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.put(
      `/company/update-section/contractId/${contractId}/sectionId/${sectionId}`,
      data,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getSalesPersonAndPM = async (departmentId?: string, onlyPM?: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/salesperson-and-pm`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        departmentId,
        onlyPM,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const addSection = async (data: any, contractId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/company/add-section/contractId/${contractId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const updateTaskSequence = async (data: {
  data: [
    {
      _id: string
      sequence: string
    }
  ]
}) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/update-task-sequence`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getAllContracts = async (projectType?: string | undefined) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/get-all-contracts`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        projectType,
      },
    })
    return response
  } catch (error: any) {
    console.error('getAllContracts error', error)
    return error?.response
  }
}

export const getAllContractsByType = async (projectType: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/contracts-by-type`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        projectType,
      },
    })
    return response
  } catch (error: any) {
    console.error('getAllContracts error', error)
    return error?.response
  }
}

export const getContractById = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/get-contract/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getContractById error', error)
    return error?.response
  }
}

export const createContract = async (data: {
  projectType: string
  contractName: string
  isDefault: boolean
  sections: any[]
  contentBlockId?: string
}) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/company/create-contract`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const deleteContract = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(
      `/company/delete-contract/${id}
    `,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )

    return response
  } catch (error: any) {
    console.error('deleteContract error', error)
    return error?.response
  }
}

export const createFinePrintContent = async (data: {
  name: string
  state: string
  projectType: string
  type: number
  content: string
  isActive: boolean
}) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/company/content`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const updateFinePrintContent = async (data: {
  name: string
  state: string
  projectType: string
  type: number
  content: string
  isActive: boolean
}) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/company/content`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const deleteFinePrintContent = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(
      `/company/content/${id}
    `,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    return error?.response
  }
}

export const getAllContent = async (data: { type: number; projectType?: string }, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/content-all/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        // projectType: data.projectType,
        type: data.type,
      },
    })
    return response
  } catch (error: any) {
    console.error('getSections error', error)
    return error?.response
  }
}

export const getSections = async (contractId?: string | undefined) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/get-sections/contractId/${contractId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getSections error', error)
    return error?.response
  }
}

export const deleteSection = async (contractId: string, sectionId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(
      `/company/delete-section/contractId/${contractId}/sectionId/${sectionId}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )

    return response
  } catch (error: any) {
    console.error('deleteSection error', error)
    return error?.response
  }
}

export const updateContract = async (
  data: {
    projectType: string
    contractName: string
    isDefault: boolean
    sections: any[]
  },
  id: string
) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/company/update-contract/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const restoreMember = async (data: I_RestoreMember) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/company/restore-member`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('restoreMember error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getCompanyCommision = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/get-company-commision`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCompanyCommision error', error)
    return error?.response
  }
}

export const upsertCompanyCommision = async (data: I_UpsertCompanyCommision) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/company/upsert-company-commision`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('upsertCompanyCommision error', error)
    return error?.response
  }
}

export const createCompany = async ({ companyName, description }: I_CreateCompany) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(
      `/company/create-company`,
      { companyName, description },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('createCompany error', error)
    return error?.response
  }
}

export const getAllCompanies = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/user`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getAllCompanies error', error)
    return error?.response
  }
}

export const updateCompanyMemberInfo = async (data: Partial<I_UpdateCompanyMemberInfo>) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/company/update-member-company-info`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('updateCompanyMemberInfo error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

// export const getCompanyInfo = async (data: I_GetCompensationInfo) => {
//   try {
//     const token: any = localStorage.getItem('token')

//     const response = await AxiosInstance.get(`/company/member/id/${data.memberId}`, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })

//     return response
//   } catch (error: any) {
//     console.error('getCompanyInfo error', error)
//     return error?.response
//   }
// }

export const getDirectReport = async (data: I_GetDirectReport) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/direct-report/member/${data.memberId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDirectReport error', error)
    return error?.response
  }
}

export const getMemberByUserId = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/get-member-detail`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getMemberByUserId error', error)
    return error?.response
  }
}

export const deleteMember = async (data: I_DeleteMember) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/company/delete-member`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })

    return response
  } catch (error: any) {
    console.error('deleteMember error', error)
    return error?.response
  }
}

export const terminateMember = async (data: I_TerminateMember) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/company/terminate-member`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })

    return response
  } catch (error: any) {
    console.error('terminateMember error', error)
    return error?.response
  }
}

export const getCompanySettings = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/get-company-setting`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCompanySettings error', error)
    return error?.response
  }
}

export const getCompanySettingForAll = async () => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/company/company-address`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCompanySettingForAll error', error)
    return error?.response
  }
}

export const upsertCompanySettings = async (data: I_UpsertCompanySettings) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/company/upsert-company-setting`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('upsertCompanySettings error', error)
    return error?.response
  }
}

export const getReferres = async (deleted: boolean, allReferrers: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/referrers/deleted/${deleted}/allReferrers/${allReferrers}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCompanyReferres error', error)
    return error?.response
  }
}

export const getAdmins = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/admins-owners`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCompanyReferres error', error)
    return error?.response
  }
}

export const createReferrer = async (data: I_CreateReferrer) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/company/create-referrer`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('create referrer error', error)
    return error?.response
  }
}

export const restoreReferrer = async (data: I_MutateReferrer) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/company/restore-referrer`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('restore referrer error', error)
    return error?.response
  }
}

export const deleteReferrer = async (data: I_MutateReferrer) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/company/delete-referrer`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })
    return response
  } catch (error: any) {
    console.error('delete referrer error', error)
    return error?.response
  }
}

export const permDeleteReferrer = async (data: I_MutateReferrer) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/company/perm-delete-referrer`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })
    return response
  } catch (error: any) {
    console.error('perm delete referrer error', error)
    return error?.response
  }
}
export const updateCompany = async (data: { imageUrl?: string; description?: string }) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/company`,
      { ...data },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateCompany error', error)
    return error?.response
  }
}
