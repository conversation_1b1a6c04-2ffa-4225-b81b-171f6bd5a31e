import { useSelector } from 'react-redux'
import { Fragment, useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { Link, NavLink, useLocation, useNavigate } from 'react-router-dom'

import * as Paths from '../../../logic/paths'
import { AddSvg, AvatarSvg, CaretSvg, CloseSvg, nhrLogoSvg, SettingsSvg } from '../../../shared/helpers/images'
import * as Styled from './style'

import clientsSvg from '../../../assets/newIcons/clients.svg'
import clockSvg from '../../../assets/newIcons/clock.svg'
import reportsSvg from '../../../assets/newIcons/reports.svg'
import actionsSvg from '../../../assets/newIcons/actions.svg'
import formIcon from '../../../assets/newIcons/form.svg'
import salesSvg from '../../../assets/newIcons/sales.svg'
import leadsSvg from '../../../assets/newIcons/lead.svg'
import teamSvg from '../../../assets/newIcons/team.svg'
import timeSvg from '../../../assets/newIcons/timeCard.svg'
import CompanySVG from '../../../assets/newIcons/company.svg'
import CrewSVG from '../../../assets/newIcons/crewPosition.svg'
import SubscriberSVG from '../../../assets/newIcons/subscriber.svg'
import LeadsSVG from '../../../assets/newIcons/leads.svg'
import DepartmentSVG from '../../../assets/newIcons/department.svg'
import TaxSVG from '../../../assets/newIcons/tax.svg'
import ImageIcon from '../../../assets/newIcons/media.svg'

import { getAllCompanies, getCompanySettingForAll, getMemberByUserId } from '../../../logic/apis/company'
import { getMemberPosition } from '../../../logic/apis/position'
import {
  setCompanies,
  setCompanySettingForAll,
  setCurrentCompany,
  setCurrentMember,
  setCurrentPosition,
  setCurrentPositionDetails,
  setCurrentRole,
  setPositionPermissions,
} from '../../../logic/redux/actions/company'
import { setIsInvited } from '../../../logic/redux/actions/invitation'
import { setPositionNotAvailable, setShowAddOptions, setShowMobileSideNav } from '../../../logic/redux/actions/ui'
import { useAppDispatch, useAppSelector } from '../../../logic/redux/reduxHook'
import { OptionItem } from '../../../shared/dropDown/style'
import { formatDateymd, getDataFromLocalStorage, isSuccess, isValidURL, notify } from '../../../shared/helpers/util'
import { useClickOutside } from '../../../shared/hooks/useClickOutside'
import useWindowDimensions from '../../../shared/hooks/useWindowDimensions'
import { FlexCol, FlexRow, RoundButton } from '../../../styles/styled'
import { SelectedOrg } from '../navbar/style'
import SelectOrgModal from '../navbar/components/SelectOrgModal'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import { SLoader } from '../../../shared/components/loader/Loader'
import { TooltipPortal } from '../../../shared/components/tooltip/TooltipPortal'
import { ROLES_OBJ, StorageKey, SubscriptionPlanType, UserRolesEnum } from '../../../shared/helpers/constants'
import { getTeamMemberRole } from '../../../logic/apis/team'
import { getCompanyPayData } from '../../../logic/apis/pieceWork'
import { getMediaSettings } from '../../../logic/apis/media'

export const createCompanyName = (name: string) => {
  // Split the name by spaces and filter out empty parts (in case of multiple spaces)
  const words = name?.trim()?.split(/\s+/)

  let companyName = ''

  // If there's only one word, take the first 3 letters of that word
  if (words?.length === 1) {
    companyName = words[0].substring(0, 3).toUpperCase()
  }
  // If there are multiple words, take the first letter of each word
  else {
    companyName = words?.map((word) => word[0].toUpperCase()).join('')
    // If the result is less than 3 letters, append more letters from the words
    if (companyName?.length < 3) {
      for (let i = 1; i < words.length; i++) {
        if (companyName?.length < 3 && words[i].length > 1) {
          companyName += words[i][1].toUpperCase()
        }
      }
    }
  }

  // Return only the first three letters
  return companyName?.substring(0, 3)
}

const Sidebar = () => {
  const { navCollapsed } = useAppSelector((state) => state.ui)

  const { isMobile, width, isTablet } = useWindowDimensions()
  const dispatch = useAppDispatch()
  const { pathname } = useLocation()
  const [nestedSection, setNestedSection] = useState('')
  const nestedPopupRef = useRef(null)
  const [height, setHeight] = useState(0)
  useClickOutside(nestedPopupRef, () => setNestedSection(''))
  const [loading, setLoading] = useState(true)

  const [showOrganizationModal, setShowOrganizationModal] = useState(false)

  const [permissions, setPermissions] = useState<any>({})
  const [report, setReport] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { companies, currentCompany, positionDetails, triggerRefetch, currentMember, roleData } = globalSelector.company
  const { isInvited } = globalSelector.invitation
  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  // const isSubcontractorRole = Number(localStorage.getItem('roleType')) === UserRolesEnum.Contractor
  const isSubcontractorRole =
    positionDetails?.symbol === 'subContractor' || getDataFromLocalStorage('position') === 'Subcontractor'

  const [currentSection, setCurrentSection] = useState('')

  const isOwnerOrAdminRole = roleData?.role === Number(ROLES_OBJ.Owner) || roleData?.role === Number(ROLES_OBJ.Admin)
  const mainRef = useRef<HTMLDivElement>(null)
  // const sideRef = useRef(null)

  // useClickOutside(sideRef, () => {
  //   dispatch(setShowMobileSideNav(false))
  // })

  useEffect(() => {
    setNestedSection('')
  }, [navCollapsed])

  const path = pathname?.split('/')[1]
  const navigate = useNavigate()

  const TeamTab = [
    {
      icon: teamSvg,
      text: 'Team',
      path: '',
      id: 'team',
      nestedItem: isProPlusPlan
        ? [
            {
              text: 'Team',
              id: 'team',
              path: Paths.teamPath,
            },
            {
              text: 'Subcontractors',
              id: 'team',
              path: Paths.subcontractorPath,
            },
            {
              text: 'Crew',
              id: 'team',
              path: Paths.crewPath,
            },
          ]
        : [
            {
              text: 'Team',
              id: 'team',
              path: Paths.teamPath,
            },

            {
              text: 'Crew',
              id: 'team',
              path: Paths.crewPath,
            },
          ],
    },
  ]

  const DashboardTab = [
    {
      icon: DepartmentSVG,
      text: 'Dashboard',
      id: 'dashboard',
      path: Paths.dashboardPath,
    },
  ]
  const TimeCardsTab = [
    {
      icon: timeSvg,
      text: 'Time Cards',
      path: '/time-cards/approve?crew=All',
      id: 'time-cards',
      // nestedItem: [
      //   { text: 'Approve Time Cards', id: 'time-cards', path: '/time-cards/approve?crew=All' },
      //   { text: 'Deleted Time Cards', id: 'time-cards', path: '/time-cards/deleted' },
      // ],
    },
  ]
  const ProjectsTab = [
    {
      icon: TaxSVG,
      text: 'Projects',
      id: 'projects',
      path: Paths.projectsPath,
    },
  ]

  const SettingsTab = [
    {
      icon: SettingsSvg,
      text: 'Settings',
      id: 'settings',
      path: Paths.settingsPath,
    },
  ]

  const ClientTab = [
    {
      icon: clientsSvg,
      text: 'Clients',
      id: 'client',
      path: Paths.clientPath,
    },
  ]

  const ContactTab = [
    {
      icon: clientsSvg,
      text: 'Contacts',
      id: 'contact',
      path: Paths.contactPath,
    },
  ]

  const ReportsTab = [
    {
      icon: reportsSvg,
      text: 'Reports',
      path: Paths.reportsPath,
      id: 'reports',
    },
  ]

  const ActionsTab = [
    {
      icon: actionsSvg,
      text: 'Actions',
      path: Paths.actionsPath,
      id: 'actions',
    },
  ]

  const FormsTab = [
    {
      icon: formIcon,
      text: 'Forms',
      path: Paths.formsPath,
      id: 'forms',
    },
  ]

  const MediaTab = [
    {
      icon: ImageIcon,
      text: 'Media',
      path: Paths.allMediaPath,
      id: 'media',
    },
  ]

  const LeadsTab = [
    {
      icon: leadsSvg,
      text: 'Leads',
      id: 'leads',
      path: Paths.leadsPath,
    },
  ]

  const SalesTab = [
    {
      icon: salesSvg,
      text: 'Sales',
      id: 'sales',
      path: Paths.salesPath,
    },
  ]

  const Operations = [
    {
      icon: LeadsSVG,
      text: 'Operations',
      id: 'operations',
      path: Paths.operationsPath,
    },
  ]
  const ClockInOut = [
    {
      icon: clockSvg,
      text: 'Clock-In/Out',
      id: 'clock-in-out',
      path: Paths.clockInOutPath,
    },
  ]
  const CompanyTab = [
    {
      icon: CompanySVG,
      text: 'Company',
      id: 'company',
      path: Paths.companyPath,
    },
  ]
  const TrackTab = [
    {
      icon: CrewSVG,
      text: 'Track',
      id: 'track',
      path: Paths.trackPath,
    },
  ]

  const SubscriberTab = [
    {
      icon: SubscriberSVG,
      text: 'Subscribers',
      id: 'subscribers',
      path: Paths.subscribersPath,
    },
  ]

  const getCompanies = async () => {
    try {
      const response = await getAllCompanies()
      if (response?.data?.statusCode === 200) {
        //  authenticate(response?.data?.data.access_token, response?.data?.data?.user?._id)
        const allCompanies = response?.data?.data?.companiesData

        dispatch(setCompanies(allCompanies))

        if (allCompanies.length > 0 && !localStorage.getItem('currentCompany')) {
          dispatch(setCurrentCompany(allCompanies[0].company))
          const name = createCompanyName(allCompanies[0]?.company?.companyName)
          localStorage.setItem(StorageKey.companyName, JSON.stringify(name))
          localStorage.setItem('currentCompany', JSON.stringify(allCompanies[0].company))
        }
      } else {
        notify(response?.data?.message, 'error')
      }
      if (localStorage.getItem('currentCompany')) {
        const currentComp: any = localStorage.getItem('currentCompany')
        dispatch(setCurrentCompany(JSON.parse(currentComp)))
      }
    } catch (error) {
      console.error('getCompanies error', error)
    } finally {
      setLoading(false)
    }
  }

  const checkIsInvited = () => {
    try {
      if (localStorage.getItem('isInvited')) {
        let isInvitedValue: any = localStorage.getItem('isInvited')
        dispatch(setIsInvited(JSON.parse(isInvitedValue)))
      }
    } catch (error) {
      console.error('checkIsInvited error', error)
    }
  }

  const getCurrentMember = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getMemberByUserId()
      // await getMemberPosition({ companyId: currentCompany._id })
      if (response?.data?.statusCode === 200) {
        let memberData = response?.data?.data?.member
        dispatch(setCurrentMember(memberData))
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getCurrentMember error', error)
    } finally {
      setLoading(false)
    }
  }

  const initCompanySettingForAll = async () => {
    try {
      const response = await getCompanySettingForAll()
      const res = await getMediaSettings()
      if (isSuccess(response)) {
        const { companySetting } = response?.data?.data
        const { mediaSetting } = res?.data?.data

        dispatch(setCompanySettingForAll({ ...companySetting, ...mediaSetting }))
      }
    } catch (error) {
      console.log(error)
    }
  }

  const getPosition = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getMemberPosition()
      const payRes = await getCompanyPayData()
      const positionResponse = response?.data?.data?.memberPosition
      const payResponse = payRes?.data?.data?.pieceWorkPositions
      if (isSuccess(response)) {
        let position = positionResponse?.position
        dispatch(setCurrentPosition(position))
        localStorage.setItem('position', JSON.stringify(position))
        dispatch(
          setCurrentPositionDetails({
            ...positionResponse,
            isPieceworkPosition: payResponse?.includes(positionResponse?._id),
            pieceworkPositions: payResponse,
          })
        )
        setLoading(false)

        const modules = ['module', 'crm', 'settings']

        const allPermissionData = positionResponse?.permissions
          ?.map((itm: any) => {
            if (modules?.includes(itm?.category)) {
              return itm?.resources
            } else {
              return itm?.resources?.map((cat: any) => ({ ...cat, name: `${cat?.name} ${itm?.category}` }))
            }
          })
          ?.flat()

        const processedDict: { [key: string]: boolean } = {}
        for (const item of allPermissionData) {
          const resource = item?.name
          const permissions = [item?.permissions]
          if (permissions.some((p: any) => [1, 2, 3].includes(p))) {
            processedDict[resource] = true
          } else if (permissions.includes(4)) {
            processedDict[resource] = false
          }
        }

        dispatch(setPositionPermissions(processedDict))
        setPermissions(processedDict)
        const reportKeys = Object.keys(processedDict).filter((key) => key.includes('report'))
        const combinedValue = reportKeys.some((key) => processedDict[key])
        setReport(combinedValue)
      } else {
        dispatch(setPositionNotAvailable(true))
      }
      // }
    } catch (error) {
      console.error('getCurrentMember error', error)
    } finally {
      setLoading(false)
    }
  }

  const dashboardPermissions = {
    crewDashboard: permissions['leads dashboard'],
    crewLeadDashboard: permissions['timecards missing/unapproved dashboard dashboard'],
    projectManagerDashboard: permissions['missing dailylogs dashboard'],
    salesPersonDashboard: permissions['opps to do dashboard'],
  }

  const showDashboard =
    Object.values(dashboardPermissions)?.length && Object.values(dashboardPermissions)?.some((item) => item)

  useEffect(() => {
    getCompanies()
    checkIsInvited()
  }, [triggerRefetch])

  useEffect(() => {
    // if (currentCompany && currentCompany._id) {
    initCompanySettingForAll()
    getCurrentMember()
    getPosition()
    // }
  }, [])

  useEffect(() => {
    if (currentMember?._id) {
      ;(async () => {
        const response = await getTeamMemberRole({ memberId: currentMember?._id })

        dispatch(setCurrentRole(response?.data?.data?.role))
      })()
    }
  }, [currentMember?._id])

  const Nav = ({ item }: { item: any }) => {
    return (
      <TooltipPortal
        isTooltipVisible={navCollapsed}
        content={item?.text}
        position="right"
        customStyle={{ marginLeft: '10px' }}
      >
        <Styled.StyledLink
          as={item?.nestedItem ? 'div' : NavLink}
          to={item?.path}
          className={item?.nestedItem ? 'nested' : ''}
          $isActive={item?.id === path}
          $isOpen={currentSection === item.id}
          $navCollapsed={navCollapsed}
          onClick={(e: any) => {
            if (!item?.path) {
              window.scrollTo(0, 0)

              if (currentSection === item.id) {
                setCurrentSection('')
              } else {
                setCurrentSection(item.id)
              }

              e.preventDefault()
              e.stopPropagation()
              setHeight(e.clientY)
              setNestedSection(item?.text)
            } else {
              navigate(item?.path)
            }
          }}
        >
          <Styled.NavItemCont navCollapsed={navCollapsed} className={item?.id === path ? 'showNested' : ''}>
            <FlexRow justifyContent="space-between">
              <FlexRow>
                <img src={item?.icon} alt={`${item?.text}-icon`} />

                <p>{item?.text}</p>
              </FlexRow>
              {item?.nestedItem && !navCollapsed && <Styled.CaretImg src={CaretSvg} alt="caret icon" />}
            </FlexRow>

            {item?.nestedItem && !navCollapsed && (
              <Styled.NestedCont
                gap="4px"
                alignItems="center"
                isProPlusPlan={isProPlusPlan}
                className={item?.id === 'time-cards' ? 'time' : ''}
                onClick={() => {
                  localStorage.setItem('currentDate', JSON.stringify(formatDateymd(new Date())))
                }}
              >
                {item?.nestedItem?.map((nestedItem: any, index: number) => (
                  <Styled.StyledLink
                    to={nestedItem?.path}
                    key={`${nestedItem?.path}-${index}`}
                    end
                    onClick={() => {
                      dispatch(setShowMobileSideNav(false))
                    }}
                  >
                    <p>{nestedItem?.text}</p>
                  </Styled.StyledLink>
                ))}
              </Styled.NestedCont>
            )}
          </Styled.NavItemCont>

          {item?.text === nestedSection && navCollapsed && (
            <>
              {createPortal(
                <Styled.NestedPopup style={{ top: height }} ref={nestedPopupRef}>
                  {item?.nestedItem?.map((link: any, idx: number) => (
                    <OptionItem
                      key={idx}
                      as={Link}
                      to={link?.path}
                      onClick={(e) => {
                        e.stopPropagation()
                        setNestedSection('')

                        localStorage.setItem('currentDate', JSON.stringify(formatDateymd(new Date())))
                      }}
                    >
                      <p>{link.text}</p>
                    </OptionItem>
                  ))}
                </Styled.NestedPopup>,
                document.body
              )}
            </>
          )}
        </Styled.StyledLink>
      </TooltipPortal>
    )
  }

  return (
    <Styled.SidebarContainer
      navCollapsed={navCollapsed}
      //  ref={sideRef}
    >
      <Styled.NavItemsWrapper navCollapsed={navCollapsed}>
        {width <= 1024 ? (
          <FlexRow justifyContent="space-between" padding="0 0 0 10px">
            <RoundButton>
              <img src={CloseSvg} alt="close icon" onClick={() => dispatch(setShowMobileSideNav(false))} />
            </RoundButton>

            {/* <RoundButton
              className="add"
              onClick={() => {
                dispatch(setShowAddOptions(true))
              }}
            >
              <img src={AddSvg} alt="add icon" />
            </RoundButton> */}
          </FlexRow>
        ) : (
          <Link to={Paths.clockInOutPath} style={{ paddingLeft: navCollapsed ? '0px' : '12px' }}>
            <FlexRow gap="6px">
              {navCollapsed ? (
                <>
                  {currentCompany?.imageUrl && isValidURL(currentCompany?.imageUrl) ? (
                    <Styled.Logo
                      navCollapsed={navCollapsed}
                      src={currentCompany?.imageUrl ? currentCompany?.imageUrl : nhrLogoSvg}
                      alt="app logo"
                    />
                  ) : (
                    <h1>{createCompanyName(currentCompany?.companyName)}</h1>
                  )}
                </>
              ) : (
                <>
                  {currentCompany?.imageUrl && isValidURL(currentCompany?.imageUrl) ? (
                    <Styled.Logo
                      navCollapsed={navCollapsed}
                      src={currentCompany?.imageUrl ? currentCompany?.imageUrl : nhrLogoSvg}
                      alt="app logo"
                    />
                  ) : null}
                  <h1>{createCompanyName(currentCompany?.companyName)}</h1>
                </>
              )}
            </FlexRow>
          </Link>
        )}

        <Styled.TabContainer>
          {loading ? (
            renderLoader(navCollapsed)
          ) : (
            <>
              {companies?.length ? (
                <>
                  {isSubcontractorRole ? null : (
                    <Styled.SidebarSection gap="8px" alignItems={navCollapsed ? 'center' : 'flex-start'}>
                      <h6>USER</h6>
                      <FlexCol
                        gap="4px"
                        alignItems={navCollapsed ? 'center' : 'flex-start'}
                        onClick={() => {
                          if (isMobile || isTablet) {
                            dispatch(setShowMobileSideNav(false))
                          }
                        }}
                      >
                        {ClockInOut.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                        {companies.length !== 0 &&
                          // (POSITIONS_TIME_CARDS.includes(currentUserPosition)) &&
                          // (!isInvited || POSITIONS_TIME_CARDS.includes(currentUserPosition)) &&
                          showDashboard &&
                          isProPlusPlan &&
                          DashboardTab?.map((item, index) => (
                            <Fragment key={index}>
                              <Nav item={item} />
                            </Fragment>
                          ))}
                      </FlexCol>
                    </Styled.SidebarSection>
                  )}
                  <Styled.SidebarSection gap="8px" alignItems={navCollapsed ? 'center' : 'flex-start'} ref={mainRef}>
                    {(permissions?.team || (permissions?.timecards && permissions?.crew)) && <h6>MAIN</h6>}
                    <FlexCol
                      gap="4px"
                      alignItems={navCollapsed ? 'center' : 'flex-start'}
                      onClick={() => {
                        if (isMobile || isTablet) {
                          dispatch(setShowMobileSideNav(false))
                        }
                      }}
                    >
                      {!!companies?.length &&
                        permissions?.team &&
                        TeamTab?.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}
                      {companies.length !== 0 &&
                        // (POSITIONS_TIME_CARDS.includes(currentUserPosition)) &&
                        // (!isInvited || POSITIONS_TIME_CARDS.includes(currentUserPosition)) &&
                        permissions.timeCards &&
                        permissions.crew &&
                        TimeCardsTab?.map((item, index) => (
                          <div
                            onClick={() => {
                              localStorage.setItem('currentDate', JSON.stringify(formatDateymd(new Date())))
                            }}
                            style={{ width: navCollapsed ? 'max-content' : '100%' }}
                            key={index}
                          >
                            <Fragment>
                              <Nav item={item} />
                            </Fragment>
                          </div>
                        ))}

                      {companies.length !== 0 &&
                        // permissions.timeCard &&
                        // permissions.crew &&
                        positionDetails?.symbol === 'Owner' &&
                        !isProPlusPlan &&
                        ProjectsTab?.map((item, index) => (
                          <div
                            onClick={() => {
                              localStorage.setItem('currentDate', JSON.stringify(formatDateymd(new Date())))
                            }}
                            style={{ width: navCollapsed ? 'max-content' : '100%' }}
                            key={index}
                          >
                            <Fragment>
                              <Nav item={item} />
                            </Fragment>
                          </div>
                        ))}

                      {/* {companies.length !== 0 &&
                // (POSITIONS_TIME_CARDS.includes(currentUserPosition)) &&
                // (!isInvited || POSITIONS_TIME_CARDS.includes(currentUserPosition)) &&
                permissions.timeCard &&
                permissions.crew &&
                Calculations?.map((item, index) => (
                  <Fragment key={index}>
                    <Nav item={item} />
                  </Fragment>
                ))} */}

                      {companies.length !== 0 &&
                        permissions.clients &&
                        isProPlusPlan &&
                        ContactTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                      {/* {companies.length !== 0 &&
                        permissions.clients &&
                        isProPlusPlan &&
                        ClientTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))} */}

                      {companies.length !== 0 &&
                        permissions.leads &&
                        isProPlusPlan &&
                        LeadsTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                      {companies.length !== 0 &&
                        permissions.sales &&
                        isProPlusPlan &&
                        SalesTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                      {companies.length !== 0 &&
                        // !isInvited &&permissions.operations&&
                        permissions.operations &&
                        isProPlusPlan &&
                        Operations.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}
                      {companies.length !== 0 &&
                        report &&
                        ReportsTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                      {companies.length !== 0 &&
                        permissions.forms &&
                        isProPlusPlan &&
                        FormsTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}
                      {companies.length !== 0 &&
                        permissions.actions &&
                        isProPlusPlan &&
                        ActionsTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}
                      {companies.length !== 0 &&
                        isProPlusPlan &&
                        permissions.media &&
                        MediaTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                      {positionDetails?.symbol === 'Owner' &&
                        permissions?.gps &&
                        isProPlusPlan &&
                        TrackTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))}

                      {/* {positionDetails?.symbol === 'Owner' &&
                    permissions?.gpsTrack &&
                    SubscriberTab.map((item, index) => (
                      <Fragment key={index}>
                        <Nav item={item} />
                      </Fragment>
                    ))} */}

                      {
                        // (positionDetails?.symbol === 'Owner' || positionDetails?.symbol === 'Admin') &&
                        //   companies.length !== 0 &&
                        isOwnerOrAdminRole &&
                          SettingsTab.map((item, index) => (
                            <Fragment key={index}>
                              <Nav item={item} />
                            </Fragment>
                          ))
                      }

                      {/* {positionDetails?.symbol === 'Owner' &&
                        CompanyTab.map((item, index) => (
                          <Fragment key={index}>
                            <Nav item={item} />
                          </Fragment>
                        ))} */}
                    </FlexCol>
                  </Styled.SidebarSection>

                  {/* {(positionDetails?.symbol === 'Owner' || positionDetails?.symbol === 'Admin') && (
                <Styled.SidebarSection gap="8px" alignItems={navCollapsed ? 'center' : 'flex-start'}>
                  <h6>SETTINGS</h6>
                  <FlexCol
                    gap="4px"
                    alignItems={navCollapsed ? 'center' : 'flex-start'}
                    onClick={() => {
                      if (isMobile || isTablet) {
                        dispatch(setShowMobileSideNav(false))
                      }
                    }}
                  >
                    {companies.length !== 0 &&
                      SettingsTab.map((item, index) => (
                        <Fragment key={index}>
                          <Nav item={item} />
                        </Fragment>
                      ))}
                  </FlexCol>
                </Styled.SidebarSection>
              )} */}
                </>
              ) : (
                <FlexCol justifyContent="flex-start" margin="60px 0 0 0">
                  {/* <h3>Please create a company</h3> */}
                  <h3></h3>
                </FlexCol>
              )}
            </>
          )}
        </Styled.TabContainer>
        <SelectedOrg className="removeBorder">
          <p>Your Organization</p>

          <FlexRow onClick={() => setShowOrganizationModal(true)}>
            <img src={AvatarSvg} alt="organization image" />
            <h5>{currentCompany?.companyName}</h5>
          </FlexRow>
        </SelectedOrg>

        {isMobile && (
          <CustomModal show={showOrganizationModal}>
            <SelectOrgModal setShowModal={setShowOrganizationModal} />
          </CustomModal>
        )}
      </Styled.NavItemsWrapper>
    </Styled.SidebarContainer>
  )
}

export default Sidebar

const renderLoader = (navCollapsed: boolean) => {
  const loader = navCollapsed ? <SLoader width={30} height={30} /> : <SLoader width={100} height={20} />
  return (
    <FlexCol gap="24px" padding="0 0 0 12px">
      <FlexCol gap="8px">
        <SLoader width={40} height={14} />

        <FlexCol gap="16px">
          {loader}
          {loader}
        </FlexCol>
      </FlexCol>
      <FlexCol gap="8px">
        <SLoader width={40} height={14} />

        <FlexCol gap="16px">
          {loader}
          {loader}
          {loader}
          {loader}
          {loader}
          {loader}
          {loader}
        </FlexCol>
      </FlexCol>
      <FlexCol gap="8px">
        <SLoader width={40} height={14} />

        <FlexCol gap="16px">
          {loader}
          {loader}
        </FlexCol>
      </FlexCol>
    </FlexCol>
  )
}
