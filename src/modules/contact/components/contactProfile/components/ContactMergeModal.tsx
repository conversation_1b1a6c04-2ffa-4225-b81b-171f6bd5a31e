import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { dayjsFormat, formatPhoneNumber, notify } from '../../../../../shared/helpers/util'
import Button from '../../../../../shared/components/button/Button'
import { colors } from '../../../../../styles/theme'
import { Types } from '../../../constant'

const ModalContainer = styled.div`
  background: ${colors.white};
  border-radius: 10px;
  padding: 30px;
  width: 95vw;
  max-width: 1200px;
  overflow-x: auto;
`

const ModalTitle = styled.h2`
  margin: 0 0 30px 0;
  color: ${colors.darkGrey};
  text-align: center;
  font-size: 24px;
  font-weight: bold;
`

const ComparisonTable = styled.table`
  width: max-content;
  border-collapse: collapse;
  margin-bottom: 30px;
  border: 1px solid ${colors.lightGrey3};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: center;
  background-color: ${colors.lightGrey2};
  color: ${colors.darkGrey};
  font-weight: 600;
  min-width: 150px;
  border: 1px solid ${colors.lightGrey3};
  border-bottom: 2px solid ${colors.lightGrey3};
`

const TableCell = styled.td`
  padding: 12px 15px;
  border: 1px solid ${colors.lightGrey3};
  border-bottom: 1px solid ${colors.lightGrey3};
  text-align: left;
  vertical-align: middle;
  max-width: 280px;
  input[type='radio'] {
    margin-right: 8px;
    vertical-align: middle;
  }
`

const FieldLabel = styled.td`
  padding: 12px 15px;
  font-weight: 600;
  background-color: ${colors.lightGrey1};
  width: 80px;
  border: 1px solid ${colors.lightGrey3};
  vertical-align: middle;
`

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
`

interface ContactMatchModalProps {
  existingContacts: any[]
  newContactData: any
  onClose: () => void
  onMerge: (data: { mergedFields: Record<string, any>; fromContact: string; toContact: string }) => void
  isNewContact?: boolean
}

const ContactMergeModal: React.FC<ContactMatchModalProps> = ({
  existingContacts = [],
  newContactData = {},
  onClose,
  onMerge,
  isNewContact,
}) => {
  const [selectedFields, setSelectedFields] = useState<Record<string, any>>({})
  const [selectedFieldSources, setSelectedFieldSources] = useState<Record<string, string>>({})
  const [selectedContactIndex, setSelectedContactIndex] = useState<number>(-1)
  const [selectedToContactId, setSelectedToContactId] = useState<string>('')

  const isBusiness = newContactData?.isBusiness
  const allFields = [
    ...(isBusiness ? ['businessName'] : []),
    'fullName',
    'phone',
    'email',
    'fullAddress',
    'nextAction',
    'type',
  ]
  console.log({
    existingContacts,
    newContactData,
  })
  useEffect(() => {
    const fields: Record<string, any> = {}
    const sources: Record<string, string> = {}
    allFields.forEach((field) => {
      const value = newContactData[field]

      if (field === 'fullAddress' && newContactData.fullAddress) {
        fields.street = newContactData.street || ''
        fields.city = newContactData.city || ''
        fields.state = newContactData.state || ''
        fields.zip = newContactData.zip || ''
        fields.fullAddress = newContactData.fullAddress || ''

        sources.street = 'you-entered'
        sources.city = 'you-entered'
        sources.state = 'you-entered'
        sources.zip = 'you-entered'
        sources.fullAddress = 'you-entered'
      } else if (field === 'fullName' && newContactData.fullName) {
        fields.fullName = newContactData.fullName
        fields.firstName = newContactData.firstName
        fields.lastName = newContactData.lastName

        sources.fullName = 'you-entered'
        sources.firstName = 'you-entered'
        sources.lastName = 'you-entered'
      } else if (field === 'nextAction' && newContactData.nextAction) {
        fields.nextAction = newContactData.nextAction
        sources.nextAction = 'you-entered'
      } else if (value) {
        fields[field] = value
        sources[field] = 'you-entered'
      }
    })
    setSelectedFields(fields)
    setSelectedFieldSources(sources)
    setSelectedContactIndex(-1)
    setSelectedToContactId('')
  }, [newContactData])

  const handleFieldSelect = (field: string, value: any, sourceKey: string, contactId?: string) => {
    if (field === 'nextAction') {
      setSelectedFields((prev) => ({ ...prev, nextAction: value }))
    } else if (field === 'fullAddress') {
      const addressValues = {
        street: value.street,
        city: value.city,
        state: value.state,
        zip: value.zip,
        fullAddress: value.fullAddress,
      }
      setSelectedFields((prev) => ({ ...prev, ...addressValues }))
    } else if (field === 'fullName') {
      const nameValues = {
        fullName: value.fullName,
        firstName: value.firstName,
        lastName: value.lastName,
      }
      setSelectedFields((prev) => ({ ...prev, ...nameValues }))
    } else {
      setSelectedFields((prev) => ({ ...prev, [field]: value }))
    }
    setSelectedFieldSources((prev) => ({ ...prev, [field]: sourceKey }))
    if (contactId) {
      setSelectedToContactId(contactId)
    }
  }

  const renderRadio = (field: string, value: any, sourceKey: string, contactId?: string) => {
    if (!value) return <span style={{ width: '20px', display: 'inline-block' }} />
    return (
      <input
        type="radio"
        name={`radio-${field}`}
        checked={selectedFieldSources[field] === sourceKey}
        onChange={() => handleFieldSelect(field, value, sourceKey, contactId)}
      />
    )
  }

  const handleSelectContact = (index: number, contactId?: string) => {
    setSelectedContactIndex(index)
    if (index >= 0 && contactId) {
      setSelectedToContactId(contactId)
    } else {
      setSelectedToContactId('')
    }
  }

  const handleMergeClick = () => {
    if (!selectedToContactId || selectedContactIndex === -1) {
      notify('Please select a contact to merge with.', 'warning')
      return
    }
    console.log({
      first: {
        mergedFields: selectedFields,
        fromContact: newContactData._id,
        toContact: selectedToContactId,
      },
    })

    onMerge({
      mergedFields: selectedFields,
      fromContact: newContactData._id,
      toContact: selectedToContactId,
    })
  }

  return (
    <ModalContainer>
      <ModalTitle>Contact Match Found</ModalTitle>

      <ComparisonTable>
        <thead>
          <tr>
            <TableHeader>Field</TableHeader>
            <TableHeader>
              <input
                type="radio"
                name="rowSelect"
                checked={selectedContactIndex === -1}
                onChange={() => handleSelectContact(-1)}
              />
              You Entered
            </TableHeader>
            {existingContacts.map((contact, index) => (
              <TableHeader key={contact._id}>
                <input
                  type="radio"
                  name="rowSelect"
                  checked={selectedContactIndex === index}
                  onChange={() => handleSelectContact(index, contact._id)}
                />
                Existing Contact {index + 1}
              </TableHeader>
            ))}
          </tr>
        </thead>
        <tbody>
          {allFields.map((field) => (
            <tr key={field}>
              <FieldLabel>
                {{
                  fullName: 'Full Name',
                  businessName: 'Business Name',
                  phone: 'Phone',
                  email: 'Email',
                  fullAddress: 'Address',
                  nextAction: 'Action',
                  type: 'Type',
                }[field] || field}
              </FieldLabel>
              <TableCell>
                {renderRadio(
                  field,
                  field === 'fullAddress'
                    ? {
                        street: newContactData.street,
                        city: newContactData.city,
                        state: newContactData.state,
                        zip: newContactData.zip,
                        fullAddress: newContactData.fullAddress,
                      }
                    : field === 'fullName'
                    ? {
                        firstName: newContactData.firstName,
                        lastName: newContactData.lastName,
                        fullName: newContactData.fullName,
                      }
                    : newContactData[field],
                  'you-entered'
                )}
                {field === 'phone'
                  ? formatPhoneNumber(newContactData[field], '')
                  : field === 'nextAction'
                  ? `${newContactData[field]?.body || ''} - ${dayjsFormat(
                      newContactData[field]?.createdAt,
                      'M/D/YY'
                    )}` || '--'
                  : field === 'type'
                  ? Object.entries(Types).find(([_, v]) => v === newContactData[field])?.[0] || ''
                  : newContactData[field] || '--'}
              </TableCell>
              {existingContacts.map((contact, idx) => (
                <TableCell key={contact._id}>
                  {renderRadio(
                    field,
                    field === 'fullAddress'
                      ? {
                          street: contact.street,
                          city: contact.city,
                          state: contact.state,
                          zip: contact.zip,
                          fullAddress: contact.fullAddress,
                        }
                      : field === 'fullName'
                      ? {
                          firstName: contact.firstName,
                          lastName: contact.lastName,
                          fullName: contact.fullName,
                        }
                      : contact[field],
                    `existing-${idx}`,
                    contact._id
                  )}
                  {field === 'phone'
                    ? formatPhoneNumber(contact[field], '')
                    : field === 'nextAction'
                    ? `${contact[field]?.body || ''} - ${dayjsFormat(contact[field]?.createdAt, 'M/D/YY')}` || '--'
                    : field === 'type'
                    ? Object.entries(Types).find(([_, v]) => v === contact[field])?.[0] || ''
                    : contact[field] || '--'}
                </TableCell>
              ))}
            </tr>
          ))}
        </tbody>
      </ComparisonTable>

      <ButtonContainer>
        <Button onClick={onClose} className="gray" width="48%">
          {isNewContact ? 'Create New Contact' : 'Cancel'}
        </Button>
        <Button onClick={handleMergeClick} className="orange" width="48%" disabled={selectedContactIndex === -1}>
          Merge Contacts
        </Button>
      </ButtonContainer>
    </ModalContainer>
  )
}

export default ContactMergeModal
