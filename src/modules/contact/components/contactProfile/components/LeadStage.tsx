import React, { useEffect, useState } from 'react'
import { updateLeadStage } from '../../../../../logic/apis/contact'
import { useParams } from 'react-router-dom'
import { getKeysFromObjects, isSuccess, notify, getValueByKeyAndMatch } from '../../../../../shared/helpers/util'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../../../../styles/styled'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import Button from '../../../../../shared/components/button/Button'
import { getStages } from '../../../../../logic/apis/sales'
import { StageGroupEnum } from '../../../../../shared/helpers/constants'

const stageSchema = Yup.object().shape({
  stage: Yup.string().required('Stage is required'),
})

const LeadStage = ({
  initFetchContact,
  fetchActivity,
  stages,
  leads,
}: {
  initFetchContact: () => void
  stages: any[]
  fetchActivity: () => void
  leads: any[]
}) => {
  const { contactId } = useParams()
  const [submitLoading, setSubmitLoading] = useState(false)

  // Fetch stages on component mount

  const handleStageChange = async (values: { stage: string }) => {
    try {
      setSubmitLoading(true)
      const stageId = getValueByKeyAndMatch('_id', values.stage, 'name', stages)
      const response = await updateLeadStage(stageId, leads?.[0]?._id!)
      if (isSuccess(response)) {
        notify('Stage changed!', 'success')
        initFetchContact()
        fetchActivity()
      }
    } catch (err) {
      notify('Failed to change stage!', 'error')
      console.log('ERR', err)
    } finally {
      setSubmitLoading(false)
    }
  }

  // Get current stage name for initial value
  const getCurrentStageName = () => {
    if (leads?.[0]?.stageId && stages.length > 0) {
      return getValueByKeyAndMatch('name', leads?.[0]?.stageId, '_id', stages) || ''
    }
    return ''
  }

  return (
    <>
      <Formik
        initialValues={{ stage: getCurrentStageName() }}
        validationSchema={stageSchema}
        onSubmit={handleStageChange}
        enableReinitialize
      >
        {({ values, errors, touched, setFieldValue }) => (
          <Form>
            <SharedStyled.ContentHeader textAlign="left" as="h3">
              Lead Stage
            </SharedStyled.ContentHeader>
            <SharedStyled.FlexRow margin="20px 0 0 0" width="100%" alignItems="center" gap="12px">
              <CustomSelect
                labelName="Stage"
                stateName="stage"
                error={touched.stage && errors.stage ? true : false}
                setFieldValue={setFieldValue}
                value={values.stage}
                dropDownData={getKeysFromObjects(stages, 'name')}
                setValue={() => {}}
                innerHeight="52px"
                margin="10px 0 0 0"
              />

              <Button
                width="max-content"
                type="submit"
                isLoading={submitLoading}
                disabled={values.stage === getCurrentStageName()}
              >
                Update Stage
              </Button>
            </SharedStyled.FlexRow>
          </Form>
        )}
      </Formik>
    </>
  )
}

export default LeadStage
