import React, { use<PERSON>allback, useEffect, useState } from 'react'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import * as Styled from './style'
import * as SharedStyled from '../../styles/styled'
import DraggableDiv from '../../shared/draggableDiv/DraggableDiv'
import {
  formatDateymd,
  getDataFromLocalStorage,
  getParentIdFromName,
  getParentNameFromId,
  isSuccess,
  notify,
} from '../../shared/helpers/util'
import { CustomModal } from '../../shared/customModal/CustomModal'
import NewStepModal from './components/newStepModal/NewStepModal'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import {
  deleteCheckpoint,
  deleteStep,
  getCheckpoint,
  permDeleteStep,
  getStages,
  getStep,
  permDeleteCheckpoint,
  restoreCheckpoint,
  restoreStep,
  updateCheckpointSequence<PERSON><PERSON>,
  updateStage,
  updateStageSequence<PERSON>pi,
  updateStepSe<PERSON><PERSON><PERSON>,
  restoreStage,
} from '../../logic/apis/sales'
import NewStageModal from './components/newStageModal/NewStageModal'
import NewCheckpointModal from './components/newCheckpointModal/NewCheckpointModal'
import EditStageModal from './components/editStageModal/EditStageModal'
import { RoundButton } from '../../shared/components/button/style'
import { DeleteSvg, EditSvg, RestoreSvg } from '../../shared/helpers/images'
import Button from '../../shared/components/button/Button'
import { ButtonCont, SettingsCont } from '../units/style'
import TabBar from '../../shared/components/tabBar/TabBar'
import { Indicator, TabButton, Title } from '../../shared/components/tabBar/style'
import { SLoader } from '../../shared/components/loader/Loader'
import { StageGroupEnum, StorageKey } from '../../shared/helpers/constants'

export interface I_Checkpoint {
  _id: string
  name: string
  sequence: number
  createdBy: string
  editable: boolean
  deleted: boolean
  stageDisplay: string[]
  isDisplay?: boolean
  stageEditable: string[]
  stageSet: string[]
  isRequire: string
  symbol: string
  requiredStage?: any
}
export interface I_Step {
  dropdownField: string
  formType?: string
  _id: string
  stageId: string
  lable?: string
  name: string
  fieldType: string
  dropDownOptions: string[]
  isDisplay: boolean
  parent: string
  isParentRequired?: boolean
  isRequire: boolean
  activityType: string
  sequence: number
  deleted: boolean
  description: string
  createdBy: string
  location: any
  projectTypeId: string[]
}

export interface Stage {
  _id: string
  name: string
  sequence: number
  code: string
  editable: boolean
  deleted: boolean
  createdBy: string
  stageGroup: string
  createdAt: string
  updatedAt: string
}
interface I_Stage {
  name: string
  _id: string
  sequence: number
}

const CrmSettings = () => {
  const [newStageModal, setNewStageModal] = useState(false)
  const [newCheckpointModal, setNewCheckpointModal] = useState(false)
  const [deletedCheckpoint, setDeletedCheckpoint] = useState(false)
  const [deletedStage, setDeletedStage] = useState(false)
  const [deletedStep, setDeletedStep] = useState(false)
  const [newStepModal, setNewStepModal] = useState(false)
  const [stepAction, setStepAction] = useState('')
  const [allStageData, setAllStageData] = useState<any[]>([])
  const [activeStage, setActiveStage] = useState<any>({
    name: '',
    description: '',
    _id: '',
    sequence: 0,
    createdBy: '',
    PMRequired: false,
    projectRequired: false,
    orderRequired: false,
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [loading, setLoading] = useState(false)
  const [stepsLoading, setStepsLoading] = useState(false)
  const [cpLoading, setCpLoading] = useState(false)
  const [groupStage, setGroupStage] = useState('leads')
  const [editStage, setEditStage] = useState(false)
  const [stepLength, setStepLength] = useState(0)
  const [checkpointLength, setCheckpointLength] = useState(0)
  const [stageLength, setStageLength] = useState(0)

  const [allSteps, setAllSteps] = useState<I_Step[]>([]) // all steps including children
  const [steps, setSteps] = useState<Array<I_Step>>([]) // only parent steps
  const [checkpoints, setCheckpoints] = useState<Array<I_Checkpoint>>([])
  const [stages, setStages] = useState<Array<typeof activeStage>>([])
  const [bool, setBool] = useState(false)

  const [stepModalData, setStepModalData] = useState<I_Step | null>(null)
  const [checkpntModalData, setCheckpntModalData] = useState<I_Checkpoint | null>(null)

  const [stagesNames, setStagesNames] = useState<string[]>([])

  useEffect(() => {
    const getStageData = async () => {
      const response = await getStages({}, false, undefined)
      setAllStageData(
        response?.data?.data?.stage
          ?.sort((a: any, b: any) => {
            const order: any = { leads: 1, sales: 2, operations: 3 }
            return order[a.stageGroup] - order[b.stageGroup]
          })
          ?.map((itm: any) => {
            return {
              name: itm?.name,
              _id: itm?._id,
            }
          })
      )
    }
    getStageData()
  }, [bool])

  const initialValues = {
    date: formatDateymd(new Date().getTime()),
    time: formatDateymd(new Date().getTime()),
  }
  useEffect(() => {
    initFetch()
  }, [groupStage, deletedStage])

  const restoreCheckpointFunc = async (body: any) => {
    try {
      const response = await restoreCheckpoint({ id: body?._id })
      const data = [{ _id: body?._id, sequence: checkpointLength }]
      if (isSuccess(response)) {
        fetchCheckpoints()
        setCheckpointLength((pre) => pre + 1)
        await updateCheckpointSequenceApi({ data })
        notify(`checkpoint restored!`, 'success')
      }
    } catch (error) {
      console.log(error)
    }
  }

  const permCheckpointFunc = async (body: any) => {
    try {
      const response = await permDeleteCheckpoint({ id: body?._id })
      if (isSuccess(response)) {
        notify(`checkpoint deleted!`, 'success')
        fetchCheckpoints()
      }
    } catch (error) {
      console.log(error)
    }
  }

  const deleteCheckpointFunc = async (body: any) => {
    try {
      const response = await deleteCheckpoint({ id: body?._id })
      if (isSuccess(response)) {
        notify(`checkpoint deleted!`, 'success')
        await updateCheckpntSequence(checkpoints)
        await fetchCheckpoints()
      }
    } catch (error) {
      console.log(error)
    }
  }

  const deleteStepFunc = async (body: any) => {
    try {
      const response = await deleteStep({ id: body?._id })
      if (isSuccess(response)) {
        notify(`step deleted!`, 'success')
        await updateStepsSequenceActive(allSteps)
        await refetchStep()
      }
    } catch (error) {
      console.log(error)
    }
  }

  const permDeleteStepFunc = async (body: any) => {
    try {
      const response = await permDeleteStep({ id: body?._id })
      if (isSuccess(response)) {
        notify(`checkpoint deleted!`, 'success')
        await refetchStep()
        // fetchCheckpoints()
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    if (activeStage?.name) {
      fetchSteps(activeStage)
    }
  }, [activeStage, deletedStep])

  const refetchStep = async () => {
    let steps: I_Step[] = []
    const res = await getStep({ stageId: activeStage?._id }, deletedStep)
    if (res?.status === 200 || res?.statusCode === 200) {
      const resSteps: I_Step[] = res?.data.data.step
      const localSteps = Array(res.length)
      resSteps.forEach((resStep: I_Step) => {
        // only without parent
        if (!resStep.parent) localSteps[resStep.sequence] = resStep
      })
      steps = localSteps.filter((step) => step) // if step is defined
      setSteps(steps)
      const allStepsLocal = [...steps]
      // push children on different locations
      resSteps.forEach((step) => {
        if (!!step.parent) {
          allStepsLocal.push(step)
        }
      })
      setAllSteps(allStepsLocal)
      return { allStepsLocal }
    }
  }
  const fetchSteps = async (stage: I_Stage) => {
    setStepsLoading(true)
    let steps: I_Step[] = []
    try {
      const response = await getStep({ stageId: stage?._id }, deletedStep)
      setStepsLoading(false)
      if (response?.status === 200 || response?.statusCode === 200) {
        const resSteps: I_Step[] = response?.data.data.step
        const localSteps: any[] = []
        if (!deletedStep) {
          setStepLength(resSteps.length)
        }
        // // ============= Old =============
        // if (deletedStep) {
        //   updateStepsSequenceForDeleted(resSteps, stepLength)
        // }
        // // ============= Old =============

        resSteps.forEach((resStep: I_Step) => {
          if (!resStep.parent) localSteps.push(resStep)

          // // ============= Old =============
          // // only without parent
          // if (!resStep.parent) localSteps[resStep.sequence] = resStep
          // // ============= Old =============
        })
        steps = localSteps.filter((step) => step)?.sort((a, b) => a.sequence - b.sequence) // if step is defined

        setSteps(steps)

        const allStepsLocal = [...steps]
        // push children on different locations
        resSteps.forEach((step) => {
          if (!!step.parent) {
            allStepsLocal.push(step)
          }
        })
        setAllSteps(allStepsLocal)
        return { allStepsLocal }
      } else {
        notify('Unable to fetch steps', 'error')
        setAllSteps([])
        setSteps([])
        return { steps }
      }
    } catch (err) {
      console.log('FETCH ERR STEP', err)
      setStepsLoading(false)
      setAllSteps([])
      setSteps([])
      return { steps }
    }
  }

  useEffect(() => {
    fetchCheckpoints()
  }, [deletedCheckpoint])

  const fetchCheckpoints = async () => {
    setCpLoading(true)
    try {
      const response = await getCheckpoint(deletedCheckpoint, groupStage)
      if (response?.statusCode === 200 || response?.status === 200) {
        let checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint

        if (!deletedCheckpoint) {
          // Set checkpoint length for active checkpoints
          setCheckpointLength(checkpointsArr.length)

          // Rearrange checkpoints
          checkpointsArr = rearrangeCheckpoints(checkpointsArr)

          setCheckpoints(checkpointsArr)
        } else {
          // For deleted checkpoints, no need to rearrange
          setCheckpoints(checkpointsArr)
        }
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
      setCpLoading(false)
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
      setCpLoading(false)
    }
  }

  const rearrangeCheckpoints = (checkpoints: I_Checkpoint[]): I_Checkpoint[] => {
    // Find the "Opportunity" and "Sale" checkpoints
    const newLeadCheckpoint = checkpoints.find((cp) => cp.name === 'Opportunity')
    const saleCheckpoint = checkpoints.find((cp) => cp.name === 'Sale')

    // Filter out "Opportunity" and "Sale" from the original array
    checkpoints = checkpoints.filter((cp) => cp.name !== 'Opportunity' && cp.name !== 'Sale')

    // Sort the remaining elements based on their sequence
    checkpoints.sort((a, b) => a.sequence - b.sequence)

    // Add "Opportunity" at index 0 and "Sale" at the end
    if (newLeadCheckpoint) {
      checkpoints.unshift(newLeadCheckpoint)
    }
    if (saleCheckpoint) {
      checkpoints.push(saleCheckpoint)
    }

    return checkpoints
  }

  // ----------------------------1------------------------------
  // const fetchCheckpoints = async () => {
  //   setCpLoading(true)
  //   try {
  //     const response = await getCheckpoint(
  //       { companyId: currentCompany._id },
  //       deletedCheckpoint,
  //       operations ? true : false
  //     )
  //     if (response?.statusCode === 200 || response?.status === 200) {
  //       const checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint
  //       // const checkPointsObj = checkpointsArr.reduce((prev, cur) => {
  //       //   return { ...prev, [cur?.name]: cur }
  //       // }, {})
  //       if (!deletedCheckpoint) {
  //         setCheckpointLength(checkpointsArr.length)
  //       }
  //       if (deletedCheckpoint) {
  //         updateCheckpntSequenceForDeleted(checkpointsArr, checkpointLength)
  //       }

  //       const localCheckptsArr = new Array(checkpointsArr.length)
  //       console.log({ localCheckptsArr }, checkpointsArr.length, checkpointsArr)
  //       checkpointsArr.forEach((checkpnt) => {
  //         localCheckptsArr[checkpnt.sequence - 1] = checkpnt
  //       })
  //       setCheckpoints(localCheckptsArr)
  //     } else {
  //       throw new Error(response?.data.message ?? 'Something went wrong!')
  //     }
  //     setCpLoading(false)
  //   } catch (err) {
  //     console.log('CHECKPOINTS Fetch failed', err)
  //     setCpLoading(false)
  //   }
  // }
  // ---------------------------chat gpt------------------------------------
  // const fetchCheckpoints = async () => {
  //   setCpLoading(true)
  //   try {
  //     const response = await getCheckpoint(
  //       { companyId: currentCompany._id },
  //       deletedCheckpoint,
  //       operations ? true : false
  //     )
  //     if (response?.statusCode === 200 || response?.status === 200) {
  //       const checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint

  //       if (!deletedCheckpoint) {
  //         // Set checkpoint length for active checkpoints
  //         setCheckpointLength(checkpointsArr.length)

  //         // Reset sequence numbers for active checkpoints
  //         const normalizedCheckpointsArr = checkpointsArr.map((checkpnt, index) => ({
  //           ...checkpnt,
  //           sequence: index + 1,
  //         }))

  //         setCheckpoints(normalizedCheckpointsArr)
  //       } else {
  //         // For deleted checkpoints, no need to normalize sequence numbers
  //         setCheckpointLength(checkpointsArr.length)
  //         setCheckpoints(checkpointsArr)
  //       }
  //     } else {
  //       throw new Error(response?.data.message ?? 'Something went wrong!')
  //     }
  //     setCpLoading(false)
  //   } catch (err) {
  //     console.log('CHECKPOINTS Fetch failed', err)
  //     setCpLoading(false)
  //   }
  // }

  // const fetchStages = useCallback(async () => {
  //   let { data: response } = await getStages({ companyId: currentCompany._id }, deletedStage, operations)
  //   if (response?.statusCode == 200) {
  //     const { stage: stages } = response.data
  //     let _stagesNames = new Array(stages?.length > 0 ? stages?.length - 1 : 0)
  //     const _stages = new Array(stages?.length > 0 ? stages?.length - 1 : 0)
  //     console.log('dsmfksndmmkjfsdf', _stagesNames, _stages)
  //     stages?.length > 0 &&
  //       stages?.forEach((stage: any) => {
  //         _stages[stage?.sequence - 1] = {
  //           ...stage,
  //           name: stage?.name,
  //           id: stage?._id,
  //           companyId: stage?.companyId,
  //         }
  //         _stagesNames[stage?.sequence - 1] = stage?.name
  //       })
  //     // const _stages = stages.map((stage: any, index: number) => ({
  //     //   ...stage,
  //     //   name: stage.name,
  //     //   id: stage._id,
  //     //   companyId: stage.companyId,
  //     // }))
  //     // const _stagesNames = stages.map((stage: any) => stage.name)

  //     setStages(_stages)
  //     setActiveStage(_stages[0])
  //     setStagesNames(_stagesNames)
  //     return { stages: _stages }
  //   } else throw new Error(response?.data.message)
  // }, [currentCompany, operations, getStages, deletedStage])

  const fetchStages = useCallback(async (): Promise<{ stages: Stage[] }> => {
    let { data: response } = await getStages({}, deletedStage, groupStage)
    if (response?.statusCode === 200) {
      const { stage: stages }: { stage: Stage[] } = response.data

      // Sort stages based on sequence
      stages?.sort((a, b) => a.sequence - b.sequence)

      // Find opp and processContract stages
      const newLeadStage: Stage | undefined =
        groupStage === StageGroupEnum.Operations
          ? stages?.find((stage) => stage.code === 'preparePacket')
          : stages?.find((stage) => stage.code === 'opp')

      const processContractStage: Stage | undefined =
        groupStage === StageGroupEnum.Operations
          ? stages?.find((stage) => stage.name === 'Completed')
          : stages?.find((stage) => stage.code === 'processContract')

      // const newLeadStage: Stage | undefined = operations
      //   ? stages?.find((stage) => stage.code === 'preparePacket')
      //   : stages?.find((stage) => stage.code === 'opp')
      // const processContractStage: Stage | undefined = operations
      //   ? stages?.find((stage) => stage.name === 'Completed')
      //   : stages?.find((stage) => stage.code === 'processContract')

      // Remove opp and processContract stages from the sorted array
      const filteredStages: Stage[] =
        groupStage === StageGroupEnum.Operations
          ? stages?.filter((stage) => stage.code !== 'preparePacket' && stage.name !== 'Completed')
          : stages?.filter((stage) => stage.code !== 'opp' && stage.code !== 'processContract')

      if (!deletedStage) {
        setStageLength(stages?.length)
      }
      // Insert opp at the beginning and processContract at the end
      const sortedStages: any = [
        newLeadStage, // opp at 0 index
        ...filteredStages,
        processContractStage, // processContract at last index
      ].filter((stage) => stage !== undefined) as Stage[] // Remove any undefined stages

      setStages(sortedStages)
      setActiveStage(sortedStages[0])
      setStagesNames(sortedStages.map((stage: any) => stage.name))

      return { stages: sortedStages }
    } else {
      throw new Error(response?.data.message)
    }
  }, [groupStage, getStages, deletedStage])

  const initFetch = async () => {
    setLoading(true)
    try {
      // if (Object.keys(currentCompany).length > 0) {
      fetchCheckpoints()
      await fetchStages()
      // }
      setLoading(false)
    } catch (error) {
      notify('Unable to fetch stages', 'error')
      console.error('getAllStages error', error)
      setLoading(false)
    }
  }

  const handleActiveStage = (newActiveStage: string, index: number) => {
    let [activeStageObj] = stages.filter((stage) => stage?.name === newActiveStage)
    setActiveStage(activeStageObj)
  }

  const restoreStageFunc = async (body: any) => {
    try {
      const response = await restoreStage({ id: body?._id })
      const data = [{ _id: body._id, sequence: stageLength - 1 }]
      if (isSuccess(response)) {
        fetchStages()
        setStageLength((pre) => pre + 1)
        await updateStageSequenceApi({ data })
        notify(`stage restored!`, 'success')
      }
    } catch (error) {
      console.log(error)
    }
  }

  const updateStageSequence = async (stages: I_Stage[]) => {
    try {
      const data = stages.map((stage, index) => {
        return { _id: stage?._id, sequence: index + 1 }
      })
      const response: any = await updateStageSequenceApi({ data }) //await
      if (!isSuccess(response)) throw new Error(response?.data?.message ?? 'Something went wrong!')
    } catch (err) {
      notify('Unable to update sequence', 'error')
      console.log(err, 'sequence updation')
    }
  }

  const restoreStepSequence = async (body: any) => {
    try {
      const res = await restoreStep({ id: body?._id })
      const data = [{ _id: body?._id, sequence: stepLength }]
      if (isSuccess(res)) {
        fetchSteps(activeStage)
        setStepLength((pre) => pre + 1)
        await updateStepSequenceApi({ data })
        notify(`step restored!`, 'success')
      }
    } catch (error) {
      console.log(error)
    }
  }

  const updateStepsSequenceForDeleted = async (steps: I_Step[], length: number) => {
    try {
      const data = steps.map((step, index) => {
        return { _id: step._id, sequence: length + index }
      })

      const response = await updateStepSequenceApi({ data })
      if (isSuccess(response)) {
        const res = await getStep({ stageId: activeStage?._id }, deletedStep)
        if (res?.status === 200 || res?.statusCode === 200) {
          const resSteps: I_Step[] = res?.data.data.step
          const localSteps = Array(res.length)
          resSteps.forEach((resStep: I_Step) => {
            // only without parent
            if (!resStep.parent) localSteps[resStep.sequence] = resStep
          })
          steps = localSteps.filter((step) => step) // if step is defined
          setSteps(steps)
          const allStepsLocal = [...steps]
          // push children on different locations
          resSteps.forEach((step) => {
            if (!!step.parent) {
              allStepsLocal.push(step)
            }
          })
          setAllSteps(allStepsLocal)
          return { allStepsLocal }
        }
      } else throw new Error(response?.data?.message ?? 'Something went wrong!')
    } catch (err) {
      notify('Unable to update sequence', 'error')
      console.log(err, 'sequence updation')
    }
  }

  const updateStepsSequenceActive = async (steps: I_Step[], onlyChild?: boolean) => {
    try {
      const data = steps.map((step, index) => {
        return { _id: step?._id, sequence: index + 1 }
      })
      const response = await updateStepSequenceApi({ data })
      if (isSuccess(response)) {
      } else throw new Error(response?.data?.message ?? 'Something went wrong!')
    } catch (err) {
      notify('Unable to update sequence', 'error')
      console.log(err, 'sequence updation')
    }
  }

  const updateStepsSequence = async (steps: I_Step[], onlyChild?: boolean) => {
    try {
      const data = steps.map((step) => {
        return { _id: step?._id, sequence: step.sequence + 1 }
      })
      const response = await updateStepSequenceApi({ data })
      if (isSuccess(response)) {
      } else throw new Error(response?.data?.message ?? 'Something went wrong!')
    } catch (err) {
      notify('Unable to update sequence', 'error')
      console.log(err, 'sequence updation')
    }
  }

  // const updateCheckpntSequenceForDeleted = async (cpnts: I_Checkpoint[], length: number) => {
  //   try {
  //     const data = cpnts.map((cpnt, index) => {
  //       return { _id: cpnt?._id, sequence: length + index }
  //     })
  //     const response = await updateCheckpointSequenceApi({  data })
  //     if (isSuccess(response)) {
  //       const response = await getCheckpoint(
  //         {  },
  //         deletedCheckpoint,
  //         operations ? true : false
  //       )
  //       if (response?.statusCode === 200 || response?.status === 200) {
  //         const checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint
  //         const localCheckptsArr = new Array(checkpointsArr.length)
  //         checkpointsArr.forEach((checkpnt) => {
  //           localCheckptsArr[checkpnt.sequence - 1] = checkpnt
  //         })

  //         setCheckpoints(localCheckptsArr)
  //       } else throw new Error(response?.data?.message ?? 'Something went wrong!')
  //     }
  //   } catch (err) {
  //     notify('Unable to update sequence', 'error')
  //     console.log(err, 'sequence updation')
  //   }
  // }

  const updateCheckpntSequence = async (cpnts: I_Checkpoint[]) => {
    try {
      const data = cpnts.map((cpnt, index) => {
        return { _id: cpnt?._id, sequence: index + 1 }
      })
      const response = await updateCheckpointSequenceApi({ data })
      if (isSuccess(response)) {
      } else throw new Error(response?.data?.message ?? 'Something went wrong!')
    } catch (err) {
      notify('Unable to update sequence', 'error')
      console.log(err, 'sequence updation')
    }
  }

  // update sequence of object to keys arrangement received as a response from cb fn
  function updateSequence<T extends { sequence: number }>(states: T[], setState: any, api: any) {
    let localState: T[] = []

    return (cb: any) => {
      // get new keys sequence
      const newStateKeys: Array<keyof T> = cb(getKeysFromObjects(states, 'name'))

      // update sequence in keys arrangement
      newStateKeys.forEach((key, newSequence) => {
        let [item] = states.filter((state: any) => state['name'] === key)
        item.sequence = newSequence
        localState.push(item)
      })

      // update original state in same arrangement
      setState(localState)

      // update in db
      api(localState)
    }
  }

  // extracts specific keys from object[] into an string[]
  const getKeysFromObjects = (obj: any[], key: string) => {
    let keysArr = obj.reduce((prev: string[], cur: any) => {
      return [...prev, cur[key]]
    }, [])
    return keysArr
  }
  const getChildSteps = (curStep: string, allSteps: I_Step[]) => {
    let localChildSteps: I_Step[] = []
    allSteps.map((step) => {
      if (step.parent === getParentIdFromName(curStep, allSteps)) {
        localChildSteps.push(step)
      }
    })
    return localChildSteps
  }

  const renderChildSteps = (curStep: string, allSteps: I_Step[]) => {
    const [childSteps, setChildSteps] = useState<I_Step[]>([])
    const handleSequenceUpdate = async (newSequence: I_Step[]) => {
      const allStepsLocal = allSteps
      allStepsLocal.forEach((step, id) => {
        newSequence.forEach((newStep, newId) => {
          if (newStep?.name === step?.name) {
            allStepsLocal[id].sequence = newId
          }
        })
      })
      updateStepsSequence(allStepsLocal)
    }

    useEffect(() => {
      const unfromatChildSteps = getChildSteps(curStep, allSteps)
      const formatChildSteps: I_Step[] = Array(unfromatChildSteps.length)
      unfromatChildSteps.forEach((step, index) => {
        // formatChildSteps[index] = step
        formatChildSteps[step.sequence] = step
      })

      setChildSteps(formatChildSteps.filter((v) => !!v))
    }, [curStep, allSteps])

    return childSteps.length ? (
      <DraggableDiv
        isEditable
        onEdit={(item: string, idx: number) => {
          setStepModalData(childSteps[idx])
          setNewStepModal(true)
          setStepAction('Edit')
        }}
        isChild
        items={getKeysFromObjects(childSteps, 'name')}
        setItems={updateSequence(childSteps, setChildSteps, handleSequenceUpdate)}
        paddingDragDiv="8px 0 8px 8px"
        renderChild={(stepKey: string, idx: number) => {
          return (
            <Styled.StepWrapper key={idx}>
              <div className="flex-child">
                <label className="label">
                  {childSteps[idx]?.name}
                  {childSteps[idx]?.isRequire && <span>*</span>}
                </label>

                <SharedStyled.FlexRow width="max-content">
                  <RoundButton
                    className="edit-wrapper"
                    onClick={() => {
                      deleteStepFunc(childSteps[idx])
                    }}
                    title="Delete"
                  >
                    <img src={DeleteSvg} alt="delete icon" />
                  </RoundButton>

                  <RoundButton
                    className="edit-wrapper"
                    onClick={() => {
                      setStepModalData(childSteps[idx])
                      setNewStepModal(true)
                      setStepAction('Edit')
                    }}
                    title="Edit"
                  >
                    <img src={EditSvg} alt="edit icon" />
                  </RoundButton>
                </SharedStyled.FlexRow>
                {/* <Styled.Link
                  className="edit-wrapper"
                  onClick={() => {
                    setStepModalData(childSteps[idx])
                    setNewStepModal(true)
                    setStepAction('Edit')
                  }}
                >
                  Edit
                </Styled.Link> */}
              </div>
            </Styled.StepWrapper>
          )
        }}
      />
    ) : null
  }

  return (
    <Styled.SalesContainer>
      {/* <SharedStyled.ContentHeader textAlign="left">CRM Settings</SharedStyled.ContentHeader>
      <SharedStyled.HorizontalDivider />

      <SharedStyled.FlexBox gap="12px" margin="10px 0px">
        <SharedStyled.Button
          type="button"
          maxWidth="200px"
          mediaHeight="40px"
          mediaFontSize="14px"
          onClick={() => setOperation(false)}
          bgColor={operations ? '#DEDEDE' : ''}
        >
          Sales
        </SharedStyled.Button>
        <SharedStyled.Button
          type="button"
          maxWidth="200px"
          mediaHeight="40px"
          mediaFontSize="14px"
          onClick={() => setOperation(true)}
          bgColor={!operations ? '#DEDEDE' : ''}
        >
          Operations
        </SharedStyled.Button>
      </SharedStyled.FlexBox> */}
      {/* <br /> */}

      {/* <Styled.FirstRow> */}
      {/* <Styled.ContentWrapper> */}

      <SettingsCont gap="24px">
        <SharedStyled.FlexCol gap="24px">
          <div>
            <SharedStyled.FlexRow justifyContent="space-between">
              <SharedStyled.SectionTitle>CRM Settings</SharedStyled.SectionTitle>
              {/* <ButtonCont>
                <Button onClick={() => setNewStageModal(true)}>Add New Stage</Button>
              </ButtonCont> */}
            </SharedStyled.FlexRow>
            <SharedStyled.FlexRow alignItems="flex-start" width="max-content" gap="24px" margin="24px 0 0 0">
              <TabButton onClick={() => setGroupStage(StageGroupEnum.Leads)}>
                <Title active={groupStage === StageGroupEnum.Leads}>Leads</Title>
                <Indicator active={groupStage === StageGroupEnum.Leads} />
              </TabButton>
              <TabButton onClick={() => setGroupStage(StageGroupEnum.Sales)}>
                <Title active={groupStage === StageGroupEnum.Sales}>Sales</Title>
                <Indicator active={groupStage === StageGroupEnum.Sales} />
              </TabButton>
              <TabButton onClick={() => setGroupStage(StageGroupEnum.Operations)}>
                <Title active={groupStage === StageGroupEnum.Operations}>Operations</Title>
                <Indicator active={groupStage === StageGroupEnum.Operations} />
              </TabButton>
            </SharedStyled.FlexRow>
          </div>

          <Styled.StagesSection>
            <SharedStyled.FlexBox
              width="100%"
              gap="10px"
              wrap="wrap"
              justifyContent="space-between"
              alignItems="center"
            >
              <Styled.Subheading textAlign="left" className="small">
                Stages
              </Styled.Subheading>

              <Button type="button" width="max-content" onClick={() => setDeletedStage(!deletedStage)}>
                {deletedStage ? 'Back' : 'See deleted stage'}
              </Button>

              {deletedStage && (
                <Button type="button" width="max-content" onClick={() => restoreStageFunc(activeStage)}>
                  Restore Stage
                </Button>
              )}

              {!deletedStage && (
                <Button
                  type="button"
                  maxWidth="200px"
                  // disabled={!activeStage?.editable}
                  // bgColor={!activeStage?.editable ? '#C1C0C5' : 'rgb(3, 169, 244)'}
                  onClick={() => setEditStage(true)}
                >
                  Edit Stage
                </Button>
              )}
              <Button type="button" maxWidth="200px" onClick={() => setNewStageModal(true)}>
                New Stage
              </Button>
            </SharedStyled.FlexBox>

            <Styled.StagesWrapper>
              {loading ? (
                <SharedStyled.FlexRow gap="14px">
                  {Array(8)
                    .fill(0)
                    .map((_, idx) => (
                      <SLoader height={40} key={idx} />
                    ))}
                </SharedStyled.FlexRow>
              ) : stages.length ? (
                <DraggableDiv
                  items={getKeysFromObjects(stages, 'name')}
                  setItems={updateSequence(stages, setStages, updateStageSequence)}
                  isStage
                  horizontal
                  renderChild={(item, index) => (
                    <SharedStyled.FlexRow justifyContent="space-between">
                      <Styled.Link
                        key={index}
                        className={activeStage?.name === item ? 'active' : ''}
                        isActive={activeStage?.name === item}
                        onClick={() => handleActiveStage(item, index)}
                      >
                        {item}
                      </Styled.Link>

                      {/* {activeStage?.editable && (
                        <img src={EditSvg} onClick={() => setEditStage(true)} className="editSvg" />
                      )} */}
                    </SharedStyled.FlexRow>
                  )}
                  dragIndexRange={[0, Object.keys(stages).length - 1]}
                />
              ) : (
                'No Stage Found'
              )}
            </Styled.StagesWrapper>
          </Styled.StagesSection>
        </SharedStyled.FlexCol>
      </SettingsCont>

      {/* </Styled.ContentWrapper> */}
      {/* </Styled.FirstRow> */}

      <SharedStyled.FlexBox width="100%" gap="24px" wrap="wrap" justifyContent="space-between">
        <>
          <Styled.StepsWrapper>
            <div className="flex-1">
              {activeStage?.name ? (
                stepsLoading ? (
                  <Styled.Subheading>
                    <SharedStyled.FlexCol gap="14px">
                      {Array(5)
                        .fill(0)
                        .map((_, idx) => (
                          <SLoader height={40} key={idx} />
                        ))}
                    </SharedStyled.FlexCol>
                  </Styled.Subheading>
                ) : (
                  <>
                    {/* <Styled.Subheading fontSize="20px"></Styled.Subheading> */}
                    {/* <SharedStyled.HorizontalDivider /> */}
                    <Styled.CheckpointWrapper>
                      <Styled.HeadingCont>
                        <Styled.Subheading>{activeStage?.name} Steps</Styled.Subheading>
                        <SharedStyled.FlexRow
                          gap="12px"
                          width="max-content"
                          justifyContent="space-between"
                          alignItems="center"
                          flexWrap="wrap"
                          className="buttonCont"
                        >
                          <Button width="max-content" onClick={() => setDeletedStep(!deletedStep)}>
                            {deletedStep ? 'Back' : 'See deleted step'}
                          </Button>

                          <Button
                            width="max-content"
                            onClick={() => {
                              setNewStepModal(true)
                              setStepAction('New')
                            }}
                          >
                            Add new step
                          </Button>
                        </SharedStyled.FlexRow>
                      </Styled.HeadingCont>

                      {steps.length ? (
                        <>
                          {!deletedStep ? (
                            <div className="checkpoints">
                              <DraggableDiv
                                isEditable
                                onEdit={(item: string, idx: number) => {
                                  setStepModalData(steps[idx])
                                  setNewStepModal(true)
                                }}
                                items={getKeysFromObjects(steps, 'name')}
                                setItems={updateSequence(steps, setSteps, updateStepsSequence)}
                                // onDelete={(item: string, idx: number) => deleteStepFunc(checkpoints[idx])}
                                renderChild={(_stepKey: string, idx: number) => {
                                  return (
                                    <Styled.StepWrapper key={idx}>
                                      <div className="flex-child">
                                        <label className="label">
                                          {steps[idx]?.name}
                                          {steps[idx]?.isRequire && <span>*</span>}
                                        </label>{' '}
                                        <SharedStyled.FlexRow width="max-content">
                                          <RoundButton
                                            className="edit-wrapper"
                                            onClick={() => {
                                              deleteStepFunc(steps[idx])
                                            }}
                                            title="Delete"
                                          >
                                            <img src={DeleteSvg} alt="delete icon" />
                                          </RoundButton>

                                          <RoundButton
                                            className="edit-wrapper"
                                            onClick={() => {
                                              setStepModalData(steps[idx])
                                              setNewStepModal(true)
                                              setStepAction('Edit')
                                            }}
                                            title="Edit"
                                          >
                                            <img src={EditSvg} alt="edit icon" />
                                          </RoundButton>
                                        </SharedStyled.FlexRow>
                                      </div>

                                      <div style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
                                        {renderChildSteps(steps[idx]?.name, allSteps)}
                                      </div>
                                    </Styled.StepWrapper>
                                  )
                                }}
                              />
                            </div>
                          ) : (
                            <div className="checkpoints">
                              <DraggableDiv
                                isEditable
                                onEdit={(item: string, idx: number) => {
                                  setStepModalData(steps[idx])
                                  setNewStepModal(true)
                                }}
                                items={getKeysFromObjects(steps, 'name')}
                                setItems={updateSequence(steps, setSteps, updateStepsSequence)}
                                // onRestore={(item: string, idx: number) => restoreCheckpointFunc(checkpoints[idx])}
                                // onPermanentDelete={(item: string, idx: number) => permCheckpointFunc(checkpoints[idx])}
                                // onDelete={(item: string, idx: number) => deleteStepFunc(checkpoints[idx])}
                                renderChild={(stepKey: string, idx: number) => {
                                  return (
                                    <Styled.StepWrapper key={idx}>
                                      <div className="flex-child">
                                        <label className="label">
                                          {steps[idx]?.name}
                                          {steps[idx]?.isRequire && <span>*</span>}
                                        </label>{' '}
                                        <SharedStyled.FlexRow width="max-content">
                                          <RoundButton
                                            onClick={() => permDeleteStepFunc(steps[idx])}
                                            title="Permanently Delete"
                                          >
                                            <img src={DeleteSvg} alt="delete icon" />
                                          </RoundButton>
                                          &nbsp;
                                          <RoundButton
                                            className="edit-wrapper"
                                            title="Restore"
                                            onClick={() => {
                                              restoreStepSequence(steps[idx])
                                            }}
                                          >
                                            <img src={RestoreSvg} alt="Restore icon" />
                                          </RoundButton>
                                        </SharedStyled.FlexRow>
                                      </div>

                                      <div style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
                                        {renderChildSteps(steps[idx]?.name, allSteps)}
                                      </div>
                                    </Styled.StepWrapper>
                                  )
                                }}
                              />
                            </div>
                          )}
                        </>
                      ) : (
                        <Styled.Subheading>No steps found!</Styled.Subheading>
                      )}
                    </Styled.CheckpointWrapper>
                  </>
                )
              ) : (
                <Styled.Subheading>No stage selected</Styled.Subheading>
              )}
            </div>
          </Styled.StepsWrapper>
        </>
        <Styled.CheckpointWrapper>
          <Styled.HeadingCont>
            <Styled.Subheading>Checkpoints</Styled.Subheading>
            <SharedStyled.FlexBox
              gap="12px"
              width="max-content"
              justifyContent="space-between"
              alignItems="center"
              flexWrap="wrap"
              className="buttonCont"
            >
              <Button width="max-content" onClick={() => setDeletedCheckpoint(!deletedCheckpoint)}>
                {deletedCheckpoint ? 'Back' : 'See deleted checkpoints'}
              </Button>
              {!deletedCheckpoint && (
                <Button width="max-content" onClick={() => setNewCheckpointModal(true)}>
                  {'Add new checkpoint'}
                </Button>
              )}
            </SharedStyled.FlexBox>
          </Styled.HeadingCont>

          {cpLoading ? (
            <SharedStyled.FlexCol gap="14px" margin="14px 0 0 0">
              {Array(5)
                .fill(0)
                .map((_, idx) => (
                  <SLoader height={40} key={idx} />
                ))}
            </SharedStyled.FlexCol>
          ) : (
            <>
              {Object.keys(checkpoints).length ? (
                <>
                  {!deletedCheckpoint ? (
                    <div className="checkpoints">
                      <DraggableDiv
                        items={getKeysFromObjects(checkpoints, 'name')}
                        isDeleteable={getKeysFromObjects(checkpoints, 'editable')}
                        isEditable
                        onEdit={(item: string, idx: number) => {
                          setCheckpntModalData(checkpoints[idx])
                          setNewCheckpointModal(true)
                        }}
                        onDelete={(item: string, idx: number) => deleteCheckpointFunc(checkpoints[idx])}
                        setItems={updateSequence(checkpoints, setCheckpoints, updateCheckpntSequence)}
                        dragIndexRange={[0, Object.keys(checkpoints).length - 1]}
                      />
                    </div>
                  ) : (
                    <div className="checkpoints">
                      <DraggableDiv
                        items={getKeysFromObjects(checkpoints, 'name')}
                        onEdit={(item: string, idx: number) => {
                          setCheckpntModalData(checkpoints[idx])
                          setNewCheckpointModal(true)
                        }}
                        setItems={updateSequence(checkpoints, setCheckpoints, updateCheckpntSequence)}
                        isPerDeleteable
                        isRestore
                        onRestore={(item: string, idx: number) => {
                          restoreCheckpointFunc(checkpoints[idx])
                        }}
                        onPermanentDelete={(item: string, idx: number) => permCheckpointFunc(checkpoints[idx])}
                        // onEdit={(item: string, idx: number) => {
                        //   setCheckpntModalData(checkpoints[idx])
                        //   setNewCheckpointModal(true)
                        // }}
                        // onDelete={(item: string, idx: number) => deleteCheckpointFunc(checkpoints[idx])}
                        // dragIndexRange={[0, Object.keys(checkpoints).length - 1]}
                      />
                    </div>
                  )}
                </>
              ) : null}
            </>
          )}
        </Styled.CheckpointWrapper>
      </SharedStyled.FlexBox>

      <CustomModal show={newStepModal}>
        <NewStepModal
          onClose={() => {
            setNewStepModal(false)
            setStepModalData(null)
          }}
          allStageData={allStageData}
          lastSequence={!stepModalData?.parent ? steps.length : getChildSteps(stepModalData?.parent, allSteps).length}
          stageId={activeStage?._id}
          onSuccess={() => fetchSteps(activeStage)}
          stepData={stepModalData}
          allSteps={getKeysFromObjects(steps, 'name')}
          steps={steps}
          allStepsForSequence={allSteps}
          stepAction={stepAction}
        />
      </CustomModal>
      <CustomModal show={newStageModal}>
        <NewStageModal
          onClose={() => setNewStageModal(false)}
          totalSequence={stages.length}
          onSuccess={() => {
            fetchStages()
            setBool((p) => !p)
          }}
          groupStage={groupStage}
        />
      </CustomModal>
      <CustomModal show={editStage}>
        <EditStageModal
          onClose={() => setEditStage(false)}
          totalSequence={stages.length}
          onSuccess={fetchStages}
          name={activeStage?.name}
          description={activeStage?.description}
          activeStage={activeStage}
          groupStage={groupStage}
          disableName={!activeStage?.editable}
          checkpointData={checkpoints}
          PMRequired={activeStage?.PMRequired}
          projectRequired={activeStage?.projectRequired}
          orderRequired={activeStage?.orderRequired}
        />
      </CustomModal>

      <CustomModal show={newCheckpointModal}>
        <NewCheckpointModal
          checkpointData={checkpntModalData}
          onClose={() => {
            setNewCheckpointModal(false)
            setCheckpntModalData(null)
          }}
          checkpoints={getKeysFromObjects(checkpoints, 'name')}
          stages={stages}
          onSuccess={fetchCheckpoints}
          groupStage={groupStage}
        />
      </CustomModal>
    </Styled.SalesContainer>
  )
}

export default CrmSettings
