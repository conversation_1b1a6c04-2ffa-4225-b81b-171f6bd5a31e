import { Field, Form, Formik } from 'formik'
import React, { useEffect, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'

import {
  createStage,
  deleteStage,
  getAllStages,
  getCheckpoint,
  getPositionMembersById,
  getStageOppsData,
  getStages,
  migrateStageOpportunity,
  updateStage,
} from '../../../../logic/apis/sales'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { onlyNumber, onlyText, onlyTextWithSpaces } from '../../../../shared/helpers/regex'
import { getDataFromLocalStorage, getValueByKeyAndMatch, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './styles'
import Button from '../../../../shared/components/button/Button'
import Toggle from '../../../../shared/toggle/Toggle'
import { StorageKey } from '../../../../shared/helpers/constants'
import { getDepartments } from '../../../../logic/apis/department'
import DeletedModal from '../../../deleted/components/deletedModal/DeletedModal'
import { CustomModal } from '../../../../shared/customModal/CustomModal'

interface I_NewStageModal {
  onClose: any
  totalSequence: number
  onSuccess: any
  name: string
  description?: string
  activeStage?: any
  groupStage?: string
  disableName?: boolean
  PMRequired: boolean
  projectRequired: boolean
  orderRequired: boolean
  // defaultCsrId: string
  checkpointData: Record<string, any>[]
}

const EditStageModal: React.FC<I_NewStageModal> = (props) => {
  const {
    onClose,
    totalSequence,
    onSuccess,
    name,
    description,
    activeStage,
    groupStage,
    disableName,
    PMRequired,
    projectRequired,
    orderRequired,
    checkpointData,
  } = props

  const [loading, setLoading] = useState(false)
  const [officeDrop, setOfficeDrop] = useState<any[]>([])
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [stageOppData, setStageOppData] = useState<any>()
  const [showMigrationModal, setShowMigrationModal] = useState(false)
  const [allStageData, setAllStageData] = useState([])
  const [stageOppLoading, setStageOppLoading] = useState(false)
  const [allCheckpoints, setAllCheckpoints] = useState([])

  const [migrateLoading, setMigrateLoading] = useState(false)

  const initialValues = {
    name: name ? name : '',
    description: description ? description : '',
    PMRequired: PMRequired ? PMRequired : false,
    projectRequired: projectRequired ? projectRequired : false,
    orderRequired: orderRequired ? orderRequired : false,
    defaultCsrId: activeStage?.defaultCsrId
      ? getValueByKeyAndMatch('name', activeStage.defaultCsrId, '_id', officeDrop)
      : '',
    sortBy: activeStage?.sortingField
      ? allCheckpoints?.find((itm) => itm?.symbol === Object.keys(activeStage?.sortingField)?.[0])?.name
      : '',
    checkpoint: activeStage?.aging
      ? allCheckpoints?.find((itm) => itm?._id === activeStage?.agingCheckpointId)?.name
      : '',
  }

  const [toggle, setToggle] = useState<boolean>(
    (activeStage?.sortingField && Object.values(activeStage?.sortingField)?.[0] === -1) || false
  )

  const [agingToggle, setAgingToggle] = useState(activeStage?.aging || false)

  const handleSubmit = async (values: typeof initialValues) => {
    try {
      const sortField = allCheckpoints?.find((item) => item?.name === values?.sortBy)?.symbol
      const sortOrder = toggle ? -1 : 1
      delete values?.sortBy
      const { name, sequence, createdBy, _id } = activeStage
      const newObject = {
        ...values,
        sequence,
        createdBy,
        stageId: _id,
        sortingField: sortField
          ? {
              [sortField]: sortOrder,
            }
          : undefined,
        stageGroup: groupStage,
        defaultCsrId:
          activeStage?.code === 'newLead' || activeStage?.code === 'opp'
            ? getValueByKeyAndMatch('_id', values?.defaultCsrId, 'name', officeDrop)
            : undefined,
        agingCheckpointId:
          values?.checkpoint && agingToggle
            ? allCheckpoints?.find((itm) => itm?.name === values?.checkpoint)?._id
            : null,
        aging: agingToggle,
      }

      const res = await updateStage(newObject)
      if (isSuccess(res)) {
        notify('Stage updated successfully!', 'success')
        onClose()
        onSuccess()
      } else throw new Error(res?.data?.message)
    } catch (error) {
      // notify('Failed to update stage!', 'error')
      console.log('Submit error', error)
    }
  }

  const fetchCheckpoints = async () => {
    try {
      const response = await getCheckpoint(false)
      if (isSuccess(response)) {
        setAllCheckpoints(response?.data?.data?.checkpoint)
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
    }
  }

  useEffect(() => {
    fetchCheckpoints()
    ;(async () => {
      try {
        setStageOppLoading(true)

        const res = await getStageOppsData(activeStage?._id)
        const stageResponse = await getStages({}, false)

        setAllStageData(stageResponse?.data?.data?.stage)

        if (isSuccess(res)) {
          setStageOppData(res?.data?.data)
        }
      } catch (error) {
        console.error('getStageOppsData error', error)
      } finally {
        setStageOppLoading(false)
      }
    })()
  }, [])

  const onDelete = async () => {
    try {
      setLoading(true)
      const { _id } = activeStage
      const newObject = { id: _id }
      const res = await deleteStage(newObject)
      if (isSuccess(res)) {
        notify(`Deleted stage!`, 'success')
        onSuccess()
        onClose()
      } else throw new Error(res?.data?.message)
    } catch (err: any) {
      notify(err?.message ?? 'Failed to delete stage!', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getPositions()
  }, [])

  const getPositions = async () => {
    try {
      const response = await getDepartments({}, false)
      if (isSuccess(response)) {
        console.log({ response })
        const departments: any[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionMembers(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionMembers = async (departmentId: string) => {
    try {
      const response = await getPositionMembersById({ departmentId }, false)
      if (isSuccess(response)) {
        setOfficeDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const newStepSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyTextWithSpaces, 'Enter Valid Name'),
  })
  const stageSchema = Yup.object().shape({
    stageId: Yup.string().required('Required'),
  })
  console.log({ activeStage })

  const handleMigrate = async (values: any) => {
    try {
      setMigrateLoading(true)
      const selectedStageId = allStageData?.find((itm: any) => itm?.name === values?.stageId)?._id
      const response = await migrateStageOpportunity(activeStage?._id, selectedStageId)
      if (isSuccess(response)) {
        notify(response?.data?.data?.message, 'success')
        onDelete()
      }
    } catch (error) {
      console.error('migrateStageOpportunity error', error)
    } finally {
      setMigrateLoading(false)
    }
  }
  return (
    <>
      <Styled.StepModalContainer>
        <Formik
          initialValues={initialValues}
          enableReinitialize={true}
          onSubmit={handleSubmit}
          validationSchema={newStepSchema}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
            return (
              <>
                <Styled.ModalHeaderContainer>
                  <SharedStyled.FlexRow>
                    <img src={UnitSvg} alt="modal icon" />
                    <SharedStyled.FlexCol>
                      <Styled.ModalHeader>Edit Stage</Styled.ModalHeader>
                    </SharedStyled.FlexCol>
                  </SharedStyled.FlexRow>
                  <Styled.CrossContainer
                    onClick={() => {
                      resetForm()
                      onClose()
                    }}
                  >
                    <CrossIcon />
                  </Styled.CrossContainer>
                </Styled.ModalHeaderContainer>
                <SharedStyled.SettingModalContentContainer>
                  <Form className="form">
                    <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px">
                      <InputWithValidation
                        labelName="Name"
                        stateName="name"
                        disabled={disableName}
                        value={values.name}
                        error={touched.name && errors.name ? true : false}
                      />

                      <InputWithValidation
                        labelName="Description"
                        stateName="description"
                        value={values.description}
                        error={touched.description && errors.description ? true : false}
                      />

                      <Checkbox
                        title="Project Manager"
                        onChange={() => setFieldValue('PMRequired', !values.PMRequired)}
                        value={values.PMRequired}
                        margin="10px 0 0 0"
                        width="100%"
                      />

                      <Checkbox
                        title="Project Required"
                        onChange={() => setFieldValue('projectRequired', !values.projectRequired)}
                        value={values.projectRequired}
                        margin="10px 0 0 0"
                        width="100%"
                      />

                      <Checkbox
                        title="Order Required"
                        onChange={() => setFieldValue('orderRequired', !values.orderRequired)}
                        value={values.orderRequired}
                        margin="10px 0 0 0"
                        width="100%"
                      />

                      <SharedStyled.FlexRow>
                        <CustomSelect
                          dropDownData={checkpointData?.map((checkpoint) => checkpoint?.name)}
                          setValue={() => {}}
                          stateName="sortBy"
                          value={values.sortBy}
                          error={false}
                          setFieldValue={setFieldValue}
                          labelName="Sort by"
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        <SharedStyled.FlexRow gap="0px" width="max-content" className="toggle" margin="10px 0 0 0">
                          <Toggle
                            title="Asc"
                            width="max-content"
                            isToggled={toggle}
                            disabled={!values?.sortBy}
                            onToggle={() => {
                              setToggle((prev) => !prev)
                            }}
                          />
                          <p className="desc">Desc</p>
                        </SharedStyled.FlexRow>
                      </SharedStyled.FlexRow>

                      {/* ================== Aging ================== */}
                      <>
                        <Toggle
                          title="Aging"
                          width="max-content"
                          isToggled={agingToggle}
                          onToggle={() => {
                            setAgingToggle((prev) => !prev)
                          }}
                        />

                        {agingToggle ? (
                          <CustomSelect
                            dropDownData={allCheckpoints?.map((checkpoint) => checkpoint?.name)}
                            setValue={() => {}}
                            stateName="checkpoint"
                            value={
                              values.checkpoint
                                ? values.checkpoint
                                : activeStage?.aging
                                ? allCheckpoints?.find((itm) => itm?._id === activeStage?.agingCheckpointId)?.name
                                : ''
                            }
                            error={false}
                            showInitialValue
                            setFieldValue={setFieldValue}
                            labelName="Checkpoint"
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />
                        ) : null}
                      </>

                      {/* ================== Aging ================== */}

                      {activeStage?.code === 'newLead' || activeStage?.code === 'opp' ? (
                        <CustomSelect
                          dropDownData={officeDrop?.map((checkpoint) => checkpoint?.name)}
                          setValue={() => {}}
                          stateName="defaultCsrId"
                          value={values.defaultCsrId}
                          error={false}
                          setFieldValue={setFieldValue}
                          labelName="Default CSR"
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />
                      ) : null}

                      <SharedStyled.FlexRow margin="24px 0 0 0" justifyContent="space-between">
                        <SharedStyled.FlexBox gap="12px">
                          <Button width="max-content" type="submit">
                            Save
                          </Button>
                          <Button
                            width="max-content"
                            className="delete"
                            onClick={() => {
                              onClose()
                            }}
                          >
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>

                        <Button
                          className="fit gray"
                          type="button"
                          onClick={() => {
                            if (stageOppData?.activeOpp) {
                              setShowConfirmModal(true)
                            } else {
                              onDelete()
                            }
                          }}
                          disabled={stageOppLoading}
                        >
                          Delete
                        </Button>
                      </SharedStyled.FlexRow>
                    </SharedStyled.FlexBox>
                  </Form>
                </SharedStyled.SettingModalContentContainer>
              </>
            )
          }}
        </Formik>
      </Styled.StepModalContainer>

      <CustomModal show={showConfirmModal}>
        <DeletedModal
          isTeamSection
          onClose={() => {
            setShowConfirmModal(false)
          }}
          onDelete={() => {
            setShowConfirmModal(false)
            setShowMigrationModal(true)
          }}
          onRestore={() => {
            setShowConfirmModal(false)
          }}
          title="Alert!!!"
          restoreString="No"
          deleteString="Yes"
        >
          <Styled.StageOppInfoCont>
            <p>This stage has:</p>
            <ul>
              <li>{stageOppData?.activeOpp} Active</li>

              <li>{stageOppData?.inactiveOpp} Inactive</li>
              <li>{stageOppData?.lostOpp} Lost Cards</li>
            </ul>
            <p>in it and cannot be deleted. Would you like to move these cards to another stage?</p>
          </Styled.StageOppInfoCont>
        </DeletedModal>
      </CustomModal>

      <CustomModal show={showMigrationModal}>
        <Formik
          initialValues={{
            stageId: '',
          }}
          enableReinitialize={true}
          onSubmit={handleMigrate}
          validationSchema={stageSchema}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors: stageErrors, touched, setFieldValue, handleSubmit: formSubmit }) => {
            return (
              <DeletedModal
                isTeamSection
                onClose={() => {
                  setShowMigrationModal(false)
                }}
                onDelete={() => {
                  formSubmit()
                }}
                onRestore={() => {
                  setShowMigrationModal(false)
                }}
                title="Stage Migration"
                restoreString="Cancel"
                deleteString="Submit"
                btnLoadingDelete={migrateLoading || loading}
              >
                <Form>
                  <CustomSelect
                    dropDownData={allStageData
                      ?.filter((itm: { name: string }) => itm?.name !== activeStage?.name)
                      ?.map((stage: { name: string }) => stage?.name)}
                    setValue={() => {}}
                    stateName="stageId"
                    value={values.stageId}
                    error={!!stageErrors?.stageId}
                    setFieldValue={setFieldValue}
                    labelName="Select Stage"
                    innerHeight="52px"
                    margin="10px 0 0 0"
                  />
                </Form>
              </DeletedModal>
            )
          }}
        </Formik>
      </CustomModal>
    </>
  )
}

export default EditStageModal
