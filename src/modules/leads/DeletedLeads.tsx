import React, { useEffect, useState } from 'react'
import useFetch from '../../logic/apis/useFetch'
import { getLeadsCompletion, getStages } from '../../logic/apis/sales'
import { StageGroupEnum } from '../../shared/helpers/constants'
import { getPercentCompleteFromId, isSuccess, nextAction, notify } from '../../shared/helpers/util'
import { AnyKey } from '../newLead/assessmentForm/LeadAssessmentForm'
import { initialBoard } from './Leads'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import HorizontalScrollableDiv from '../../shared/scrollableDiv/ScrollableDiv'
import { useNavigate } from 'react-router-dom'
import { SLoader } from '../../shared/components/loader/Loader'
import { getLeads } from '../../logic/apis/contact'

const DeletedLeads = () => {
  const [loading, setLoading] = useState(false)
  const [stages, setStages] = useState()
  const [leads, setLeads] = useState()
  const navigate = useNavigate()

  const [boardValue, setBoardValue] = useState(initialBoard)
  const initFetch = async () => {
    setLoading(true)
    try {
      const [stagesRes, resLeads] = await Promise.all([
        getStages({}, false, StageGroupEnum?.Leads),
        getLeads('active', true),
      ])

      if (isSuccess(resLeads) && isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        let newBoard = new Array(stages?.length - 1)
        setStages(stages)

        // handle opportunity response
        const { leads } = resLeads?.data?.data
        let stageNames: AnyKey = {}
        stages.forEach((stage: any) => {
          // let item = {
          //   title: stage.name,
          //   cards: [],
          //   id: stage.sequence,
          //   description: stage.description,
          //   sortField: stage?.sortingField ? Object.keys(stage?.sortingField)[0] : undefined,
          //   sortOrder: stage?.sortingField ? Object.values(stage?.sortingField)[0] : undefined,
          // }
          let item = {
            title: stage.name,
            cards: [],
            id: stage.sequence, // TODO: change to id
          }
          newBoard[stage.sequence - 1] = item
          stageNames[stage._id] = stage
        })
        leads.forEach((lead: any, idx: number) => {
          stages.forEach((stage: any) => {
            if (stage._id === lead.stageId) {
              const cards = newBoard[stage.sequence - 1].cards
              newBoard[stage.sequence - 1].cards = [
                ...cards,
                {
                  ...lead,
                  id: cards?.length + 1,
                },
              ]
            }
          })
        })

        setLeads(leads)
        setBoardValue({
          columns: newBoard,
        })
        setLoading(false)
      } else {
        // if (!isSuccess(stagesRes)) throw new Error(stagesRes?.data.message)
        if (!isSuccess(resLeads)) throw new Error(resLeads?.data.message)
      }
    } catch (error: any) {
      console.error('getAllStages error', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    initFetch()
  }, [])

  const StagesBoard = () => {
    const handleItemClick = (item: any) => {
      navigate(`/contact/profile/${item._id}/${true}`)
    }

    const stageLoading = { columns: new Array(5).fill({ cards: new Array(10).fill('1') }) }
    return (
      <>
        {loading
          ? stageLoading.columns.map((column, idx) => {
              return (
                <Styled.StagesBoard key={idx} className="loading">
                  <h2>
                    <SLoader />
                  </h2>
                  <ul className="list-container">
                    {column.cards?.map((card: any, idx2: number) => (
                      <SLoader height={48} key={idx2} />
                    ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })
          : boardValue.columns.map((column: any) => {
              return (
                <Styled.StagesBoard key={column.id}>
                  <SharedStyled.FlexBox margin="10px 0 0 10px" gap="5px" alignItems="center">
                    <SharedStyled.TooltipContainer
                      width="180px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="90px"
                      positionBottomDecs="unset"
                      // positionRightDecs="20px"
                      positionTopDecs="20px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">{column?.description ?? ''}</span>
                      <SharedStyled.Text fontSize="14px" fontWeight="600">
                        {column.title}
                      </SharedStyled.Text>
                    </SharedStyled.TooltipContainer>
                  </SharedStyled.FlexBox>

                  <ul className="list-container">
                    {column.cards
                      ?.sort((a: any, b: any) => {
                        const dateA = new Date(a?.createdAt)?.getTime()
                        const dateB = new Date(b?.createdAt)?.getTime()
                        return dateB - dateA
                      })

                      ?.map((card: any, idx2: number) => (
                        <Styled.ListItem
                          key={idx2}
                          className="list-item"
                          onClick={() => handleItemClick(card)}
                          borderColor={nextAction(card)}
                        >
                          <h3>{card?.fullName}</h3>
                        </Styled.ListItem>
                      ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })}
      </>
    )
  }

  return (
    <>
      <Styled.BoardContainer marginTop="10px">
        <SharedStyled.FlexRow justifyContent="space-between" margin="0 0 20px 0">
          <SharedStyled.SectionTitle className="opportunity">Deleted Leads</SharedStyled.SectionTitle>
        </SharedStyled.FlexRow>
        <HorizontalScrollableDiv>
          <StagesBoard />
        </HorizontalScrollableDiv>
      </Styled.BoardContainer>
    </>
  )
}

export default DeletedLeads
