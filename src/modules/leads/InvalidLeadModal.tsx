import React, { useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../styles/styled'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { isSuccess, notify } from '../../shared/helpers/util'
import Button from '../../shared/components/button/Button'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import { StepModalContainer } from '../newLead/lostModal/styles'
import { CrossContainer, ModalHeader, ModalHeaderContainer } from './style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { IntendWidth } from '../contact/style'
import { markLeadAsActive, markLeadAsInvalid, updateContactLeadStatus } from '../../logic/apis/contact'

interface I_InvalidLeadModal {
  onClose: () => void
  onComplete: () => void
  isInvalid: boolean
  setIsInvalid: React.Dispatch<React.SetStateAction<boolean>>
  // fetchActivity: () => Promise<void>
  // initFetchContact: () => Promise<void>
  leadId: string
}

interface I_initialValues {
  invalidLeadReason: string
  invalidLeadNote: string
}

const InvalidLeadModal: React.FC<I_InvalidLeadModal> = (props) => {
  const { onClose, onComplete, isInvalid, setIsInvalid, leadId } = props
  const initialValues: I_initialValues = {
    invalidLeadReason: '',
    invalidLeadNote: '',
  }
  const invalidLeadSchema = Yup.object().shape({
    invalidLeadReason: !isInvalid ? Yup.string().required('Required') : Yup.string(),
    invalidLeadNote: !isInvalid
      ? Yup.string().when('invalidLeadReason', {
          is: 'Other (Describe in notes)',
          then: Yup.string().required('Required'),
          otherwise: Yup.string(),
        })
      : Yup.string().notRequired(),
  })

  const { contactId } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    try {
      const response = await (!isInvalid
        ? markLeadAsInvalid(
            {
              invalidLeadReason:
                values.invalidLeadReason === 'Other (Describe in notes)'
                  ? values.invalidLeadNote
                  : values.invalidLeadReason,
            },
            leadId
          )
        : markLeadAsActive(leadId))

      if (isSuccess(response)) {
        setIsInvalid((prev: boolean) => !prev)
        // fetchActivity()
        notify(`Lead marked as ${isInvalid ? 'valid' : 'invalid'}!`, 'success')
        setLoading(false)
        onComplete()
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log(err)
    } finally {
      onClose()
      setLoading(false)
    }
  }

  return (
    <StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={invalidLeadSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          return (
            <>
              <ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <ModalHeader>{isInvalid ? 'Mark as Valid' : 'Mark as Invalid'} Lead</ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </CrossContainer>
              </ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    {!isInvalid && (
                      <>
                        <CustomSelect
                          labelName="Invalid Lead Reason*"
                          stateName="invalidLeadReason"
                          value={values?.invalidLeadReason || ''}
                          error={!!(touched?.invalidLeadReason && errors?.invalidLeadReason)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          dropDownData={[
                            '',
                            'Current Client',
                            'Looking For Work',
                            'Outside Service Area',
                            'Purchase Material Only',
                            'Service Not Provided',
                            'Spam',
                            'Unreachable',
                            'Vendor',
                            'Warranty Call',
                            'Other (Describe in notes)',
                          ]}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        {values?.invalidLeadReason === 'Other (Describe in notes)' && (
                          <IntendWidth>
                            <InputWithValidation
                              labelName="Invalid Lead Notes*"
                              stateName="invalidLeadNote"
                              error={touched.invalidLeadNote && errors.invalidLeadNote ? true : false}
                            />
                          </IntendWidth>
                        )}
                      </>
                    )}
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" maxWidth="150px" isLoading={loading}>
                        Submit
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </StepModalContainer>
  )
}

export default InvalidLeadModal
