import { useSelector } from 'react-redux'
import { getAllLeads, getStages } from '../../logic/apis/sales'
import { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { nextAction, isSuccess } from '../../shared/helpers/util'
import HorizontalScrollableDiv from '../../shared/scrollableDiv/ScrollableDiv'
import { I_Opportunity } from '../opportunity/Opportunity'
import { AnyKey } from '../opportunity/components/assessmentForm/AssessmentForm'
import { initialBoard } from './InactiveLeads'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import { SLoader } from '../../shared/components/loader/Loader'
import { StageGroupEnum } from '../../shared/helpers/constants'
import { getLeads } from '../../logic/apis/contact'

const LostLeads = () => {
  const globalSelector = useSelector((state: any) => state)
  const { positionDetails } = globalSelector.company
  const { navCollapsed } = globalSelector.ui
  const [loading, setLoading] = useState(true)
  const [stage, setStages] = useState<I_Opportunity[]>([])

  const navigate = useNavigate()

  const [boardValue, setBoardValue] = useState(initialBoard)

  const StagesBoard = () => {
    const handleItemClick = (item: any) => {
      navigate(`/contact/profile/${item.contactId}/${false}`)
    }

    const stageLoading = { columns: new Array(5).fill({ cards: new Array(10).fill('1') }) }
    console.log({ boardValue })
    return (
      <>
        {loading
          ? stageLoading.columns.map((column, idx) => {
              return (
                <Styled.StagesBoard key={idx} className="loading">
                  <h2>
                    <SLoader />
                  </h2>
                  <ul className="list-container">
                    {column.cards?.map((card: any, idx2: number) => (
                      <SLoader height={48} key={idx2} />
                    ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })
          : boardValue.columns.map((column, idx) => {
              return (
                <Styled.StagesBoard key={column.id}>
                  <h2 className="heading">{column.title}</h2>
                  <ul className="list-container" style={{}}>
                    {column.cards
                      ?.sort(
                        (a: any, b: any) => new Date(b?.newLeadDate)?.getTime() - new Date(a?.newLeadDate)?.getTime()
                      )
                      ?.map((card: any, idx2: number) => (
                        <Styled.ListItem
                          key={idx2}
                          className="list-item"
                          onClick={() => handleItemClick(card)}
                          borderColor={nextAction(card)}
                        >
                          <h3>{card?.fullName}</h3>
                        </Styled.ListItem>
                      ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })}
      </>
    )
  }

  const getInactiveOpportunities = async () => {
    const [stagesRes, leadRes] = await Promise.all([
      getStages({}, false, StageGroupEnum.Leads),
      getAllLeads({
        deleted: false,
        status: 'lost',
      }),
    ])

    if (isSuccess(stagesRes) && isSuccess(leadRes)) {
      const { stage: stages } = stagesRes.data.data
      let newBoard = new Array(stages.length - 1)
      setStages(stagesRes.data.data.stage)
      // handle leads response
      const { leads } = leadRes?.data?.data
      let stageNames: AnyKey = {}
      stages.forEach((stage: any) => {
        let item = {
          title: stage.name,
          cards: [],
          id: stage.sequence, // TODO: change to id
        }
        newBoard[stage.sequence - 1] = item
        stageNames[stage._id] = stage
      })
      leads.forEach((lead: any, idx: number) => {
        stages.forEach((stage: any) => {
          if (stage._id === lead.stageId) {
            const cards = newBoard[stage.sequence - 1].cards
            newBoard[stage.sequence - 1].cards = [
              ...cards,
              {
                ...lead,
                id: cards?.length + 1,
              },
            ]
          }
        })
      })
      setBoardValue({
        columns: newBoard,
      })
      setLoading(false)
    } else {
      if (!isSuccess(stagesRes)) throw new Error(stagesRes?.data.message)
      if (!isSuccess(leadRes)) throw new Error(leadRes?.data.message)
    }
  }

  useEffect(() => {
    getInactiveOpportunities()
  }, [])
  return (
    <Styled.InactiveCont width={navCollapsed ? '128px' : '304px'}>
      <SharedStyled.FlexCol gap="12px" className="container">
        <SharedStyled.SectionTitle>Lost Leads</SharedStyled.SectionTitle>

        <HorizontalScrollableDiv>
          <StagesBoard />
        </HorizontalScrollableDiv>
      </SharedStyled.FlexCol>
    </Styled.InactiveCont>
  )
}

export default LostLeads
