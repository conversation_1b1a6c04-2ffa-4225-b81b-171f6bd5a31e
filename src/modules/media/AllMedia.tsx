import { DropdownContainer, DropdownContent } from '../../shared/dropdownWithCheckboxes/style'
import { CenterContainer, FlexRow, Loader, TooltipContainer } from '../../styles/styled'
import { FilterContainer } from '../operations/style'
import CheckboxList from '../track/components/CheckboxList'
import FilterSvg from '../../assets/newIcons/filter.svg'
import { useEffect, useMemo, useRef, useState } from 'react'
import useFetch from '../../logic/apis/useFetch'
import { getAllMedia } from '../../logic/apis/media'
import { AllMediaCont, Timestamp, VideoCard, VideoGrid } from './style'
import { MediaType, renderMedia } from './Media'
import { dayjsFormat } from '../../shared/helpers/util'
import { colors } from '../../styles/theme'
import MediaPreview from './components/MediaPreview'
import { getSelectedFilters } from '../reports/productionReport/ProductionReport'
import { useLocation, useSearchParams } from 'react-router-dom'
import Tag from '../../shared/components/tag'
import { useSelector } from 'react-redux'
import { SLoader } from '../../shared/components/loader/Loader'

const typeDropdown = [
  {
    name: 'Photos',
    _id: 'image',
  },
  {
    name: 'Videos',
    _id: 'video',
  },
  {
    name: 'Documents',
    _id: 'application',
  },
]

const MediaSkeleton = () => (
  <VideoCard>
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <SLoader
        width={100}
        height={200}
        isPercent
        skeletonStyle={{
          aspectRatio: '1/1',
          borderRadius: '8px',
        }}
      />
    </div>
  </VideoCard>
)

const MediaGridSkeleton = () => (
  <VideoGrid>
    {Array.from({ length: 15 }).map((_, index) => (
      <MediaSkeleton key={index} />
    ))}
  </VideoGrid>
)

const AllMedia = () => {
  const [showFilter, setShowFilter] = useState(false)
  const ref = useRef(null)
  const [showPreview, setShowPreview] = useState(false)
  const [limit, setLimit] = useState(20)
  const [selectedMedia, setSelectedMedia] = useState<any>({})
  const [typeSelected, setTypeSelected] = useState<any>({})
  const [tagSelected, setTagSelected] = useState<any>({})
  const [bool, setBool] = useState(false)
  const [userSelected, setUserSelected] = useState<any>({})
  const [hasLoaded, setHasLoaded] = useState(false)

  const mediaTypeFilters = getSelectedFilters(typeSelected)
  const tagFilters = getSelectedFilters(tagSelected)
  const userFilters = getSelectedFilters(userSelected)
  const { companySettingForAll } = useSelector((state: any) => state.company)

  const tagsData = useMemo(() => {
    return companySettingForAll?.tags?.map((itm: any) => ({ name: itm, _id: itm }))
  }, [companySettingForAll?.tags])

  const { data, loading } = useFetch({
    fetchFn: () =>
      getAllMedia({
        limit: limit ? limit : undefined,
        types: mediaTypeFilters?.length ? mediaTypeFilters?.join(',') : undefined,
        tags: tagFilters?.length ? tagFilters?.join(',') : undefined,
        createdBy: selectedMemberId?.length ? selectedMemberId?.join(',') : undefined,
      }),
    refetchTrigger: bool,
  })

  useEffect(() => {
    if (data?.media?.length) {
      setHasLoaded(true)
    }
  }, [data?.media?.length])

  const uniqueValues = useMemo(() => {
    const usersMap = new Map()

    data?.media?.forEach((type: any) => {
      if (type?.createdBy?._id && type?.createdBy?.name) {
        usersMap.set(type.createdBy._id, {
          _id: type.createdBy.name,
          name: type.createdBy.name,
          userId: type.createdBy._id,
        })
      }
    })

    return {
      users: Array.from(usersMap.values()), // Convert Map values to an array
    }
  }, [data?.media?.length])

  var selectedMemberId = useMemo(() => {
    return uniqueValues.users?.filter((user) => userFilters.includes(user?._id))?.map((itm) => itm?.userId)
  }, [userFilters?.length])

  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleFilterChange = (selectedItems: { [key: string]: boolean }, fn: any) => {
    fn(selectedItems)
    setBool((p) => !p)
  }

  const handleCloseClick = (filter: any, fn: Function, arr: any) => {
    fn({
      ...arr,
      [filter]: false,
    })
    setBool((p) => !p)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  useEffect(() => {
    if (!ref.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const intersecting = entry.isIntersecting
          if (intersecting) {
            const button = document.getElementById('load')
            button?.click()
          }
        })
      },
      {
        threshold: 1,
      }
    )

    observer.observe(ref.current)

    return () => observer.disconnect()
  }, [ref?.current])

  return (
    <AllMediaCont>
      <FlexRow className="tag-cont" justifyContent="space-between" margin="0 0 10px 0">
        <FlexRow className="tags">
          {mediaTypeFilters?.map((itm) => (
            <Tag
              itm={{ name: MediaType[itm], type: 'Type' }}
              onClose={() => handleCloseClick(itm, setTypeSelected, typeSelected)}
              showRemoveIcon
              key={itm}
            />
          ))}

          {tagFilters?.map((itm) => (
            <Tag
              itm={{ name: itm, type: 'Tag' }}
              onClose={() => handleCloseClick(itm, setTagSelected, tagSelected)}
              showRemoveIcon
              key={itm}
            />
          ))}
          {userFilters?.map((itm) => (
            <Tag
              itm={{ name: itm, type: 'User' }}
              onClose={() => handleCloseClick(itm, setUserSelected, userSelected)}
              showRemoveIcon
              key={itm}
            />
          ))}

          <p></p>
        </FlexRow>

        <DropdownContainer ref={dropdownRef} className="filter">
          <img
            src={FilterSvg}
            className="filter-icon"
            alt="filter icon"
            style={{ width: '20px', cursor: 'pointer' }}
            onClick={() => setShowFilter((p) => !p)}
          />

          {showFilter ? (
            <DropdownContent style={{ width: '280px', right: '0px' }}>
              <h3>Filter by</h3>
              <FilterContainer
                margin="10px 0 0 0"
                gap="0px"
                justifyContent="flex-start"
                onClick={(e) => e.stopPropagation()}
                className="media-filter"
              >
                <CheckboxList
                  className="first"
                  title=""
                  data={[]}
                  checkedItems={{}}
                  onSelectionChange={(_val) => {
                    setBool((p) => !p)
                    setTypeSelected([])
                    setTagSelected([])
                    setUserSelected([])
                  }}
                  allText="All"
                  isCheckedAll={!mediaTypeFilters?.length && !tagFilters?.length && !userFilters?.length}
                />
                <CheckboxList
                  title="Type"
                  className="first"
                  data={typeDropdown}
                  checkedItems={typeSelected}
                  onSelectionChange={(val) => {
                    handleFilterChange(val, setTypeSelected)
                  }}
                  hideAllCheckbox
                />
                <CheckboxList
                  title="People"
                  data={uniqueValues.users}
                  checkedItems={userSelected}
                  onSelectionChange={(val) => {
                    handleFilterChange(val, setUserSelected)
                  }}
                  hideAllCheckbox
                />
                <CheckboxList
                  checkedItems={tagSelected}
                  title="Tags"
                  data={tagsData}
                  hideAllCheckbox
                  onSelectionChange={(val) => {
                    handleFilterChange(val, setTagSelected)
                  }}
                />
              </FilterContainer>
            </DropdownContent>
          ) : null}
        </DropdownContainer>
      </FlexRow>

      <>
        {loading && !hasLoaded ? (
          <MediaGridSkeleton />
        ) : !data?.media?.length ? (
          <CenterContainer>
            <FlexRow justifyContent="center">
              <h1>No data found</h1>
            </FlexRow>
          </CenterContainer>
        ) : (
          <VideoGrid>
            {data?.media?.map((media: any) => (
              <VideoCard key={media?._id}>
                <div
                  style={{ width: '100%', height: '100%', position: 'relative' }}
                  onClick={() => {
                    setSelectedMedia(media)
                    setShowPreview(true)
                  }}
                >
                  {renderMedia(media?.url, media?.mimetype)}

                  <Timestamp className="all-media">
                    <div className="fileName">
                      <TooltipContainer
                        width="220px"
                        positionLeft="0px"
                        positionBottom="0px"
                        positionLeftDecs="100px"
                        positionBottomDecs="20px"
                        positionTopDecs="-40px"
                        fontSize="14px"
                      >
                        <span className="tooltip-content">{media?.name}</span>

                        <span className="name">{media?.name}</span>
                      </TooltipContainer>{' '}
                      <TooltipContainer
                        width="160px"
                        positionLeft="0px"
                        positionBottom="0px"
                        positionLeftDecs="40px"
                        positionBottomDecs="20px"
                        positionTopDecs="-40px"
                        fontSize="14px"
                      >
                        <span className="tooltip-content">{dayjsFormat(media.createdAt, 'hh:mma M/D/YY')}</span>

                        <span
                          style={{
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {dayjsFormat(media.createdAt, 'hh:mma M/D')}
                        </span>
                      </TooltipContainer>
                    </div>
                  </Timestamp>
                </div>
              </VideoCard>
            ))}
          </VideoGrid>
        )}
      </>

      <FlexRow ref={ref} justifyContent="center" margin="30px 0" className="loaderCont">
        {data?.media?.length >= data?.pagination?.totalItems || !data?.media?.length ? null : (
          <Loader
            onClick={() => {
              setLimit(data?.media?.length + 20)
              setBool((p) => !p)
            }}
            id="load"
            color={colors.darkGrey}
            width="22px"
            height="22px"
          />
        )}
      </FlexRow>

      {showPreview ? (
        <MediaPreview
          allMedia={[
            {
              ...selectedMedia,
              user: selectedMedia?.createdBy?.name,
              id: selectedMedia?.createdBy?.userImageUrl?._id,
              userImage: selectedMedia?.createdBy?.userImageUrl?.imageUrl,
              imageId: selectedMedia?._id,
            },
          ]}
          selectedIndex={0}
          isMediaSection
          isShareView
          isAllMedia
          allTags={companySettingForAll?.tags}
          onClose={() => {
            setSelectedMedia({})
            setShowPreview(false)
          }}
          info={{
            po: data?.PO,
            clientName: `${data?.firstName || ''} ${data?.lastName || ''}`,
          }}
        />
      ) : null}
    </AllMediaCont>
  )
}

export default AllMedia
