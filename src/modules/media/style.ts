import styled from 'styled-components'
import { screenSizes } from '../../styles/theme'
import Placeholder from '../../assets/newIcons/placeholder.png'
import { FlexRow } from '../../styles/styled'
import { Nue } from '../../shared/helpers/constants'
import { rgba } from 'polished'

export const SectionCont = styled.section`
  h2 {
    margin: 20px 0;
  }

  .media-filter {
    p {
      margin: 6px 0;
      font-family: ${Nue.medium};
    }

    .first {
      margin-top: 0;
    }
  }

  .stats {
    margin: 0 auto;
    margin-top: 10px;
    justify-content: space-between;

    span {
      cursor: pointer;
    }
  }

  .loader {
    width: 30px;
    height: 30px;
    left: 20px;
    position: static;
  }

  .loader-cont {
    height: fit-content;
  }

  .logo {
    margin: 0 auto;
    margin-bottom: 20px;

    @media (max-width: ${screenSizes.XS}px) {
      width: 80%;
    }
  }

  .filter-cont {
    flex-direction: column;
    @media (min-width: ${screenSizes.XS}px) {
      flex-direction: row;
    }
  }

  .tag-cont {
    max-width: 100%;

    @media (max-width: ${screenSizes.XS}px) {
      align-self: flex-end;
      .filter {
        width: max-content;
        margin-left: auto;
      }
    }
  }

  .tags {
    flex-wrap: wrap;
    @media (min-width: ${screenSizes.XS}px) {
      flex-wrap: nowrap;
    }
  }

  &.share-media {
    overflow: hidden;
    padding-bottom: 40px;
  }

  .download-loader {
    .loader {
      width: 24px;
      height: 24px;
    }
  }
`

export const MediaCont = styled.div`
  margin: 0 auto;
  margin-top: 16px;
`

export const VideoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;

  @media (min-width: 321px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (min-width: ${screenSizes.XS}px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(5, 1fr);
  }
`

export const VideoCard = styled.div<{ isSelected?: boolean }>`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;

  img,
  video,
  iframe {
    object-fit: contain;
    aspect-ratio: 1/1;
    width: 100%;
    display: block;
    cursor: pointer;
  }

  iframe {
    height: 210px;
  }

  &.isSelected {
    outline: 2px solid blue;
    border-radius: 8px;
  }
  input {
    position: absolute;
    top: 8px;
    left: 8px;
    transform: scale(1.5);
  }

  .fileName {
    display: flex;
    padding: 0 4px;
    justify-content: space-between;
    align-items: center;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    position: absolute;
    bottom: 0px;
    width: 100%;
    background-color: ${rgba('#3c3d3f', 0.9)};
    span {
      color: white;
    }
  }
`

export const ThumbnailWrapper = styled.div`
  position: relative;
  width: 100px;
  aspect-ratio: 4 / 3;
  overflow: hidden;
  border-radius: 0.5rem;
`

export const ThumbnailImage = styled.img`
  object-fit: cover;
`

export const Timestamp = styled.div`
  font-size: 0.875rem;
  width: 100%;
  cursor: default;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #4b5563;
  /* margin: 10px 0; */

  span:not(.tooltip-content) {
    font-size: 10px;
    user-select: none;

    @media (min-width: ${screenSizes.XS}px) {
      font-size: 12px;
    }
  }

  .name {
    display: inline-block;
    justify-self: center;
    white-space: nowrap;
    width: 100px;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;

    @media (min-width: ${screenSizes.XS}px) {
      width: 145px;
    }
  }

  &.all-media {
    .name {
      width: 140px;
      @media (min-width: 320px) {
        width: 100px;
      }
    }
  }
`

export const MediaContainer = styled.div`
  max-width: 100%;
  margin: 0 auto;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 0.5rem;

  .edit-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    padding: 6px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 0.6);
    }
  }

  &.preview {
    img {
      /* width: 120px;
      height: 80px; */
      flex-shrink: 0;
      background-size: cover;
      background-position: center;
      cursor: pointer;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }

    .video,
    .pdf {
      padding: 16px;
    }

    .video {
      filter: contrast(0.5);
    }
  }

  &.pdf-cont {
    .pdf-overlay {
      width: 30px;
      aspect-ratio: 1/1;
      position: absolute;
      bottom: 50px;
      left: 10px;
    }
  }
`

export const VideoThumbnailCont = styled.div`
  width: 100%;
  position: relative;

  .play-overlay {
    position: absolute;
    width: 70px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 70px;
  }
`

export const StyledImage = styled.img<any>`
  max-width: 100%;
  border-radius: 8px;
  background-position-x: -30px;
  /* background-image: url(${Placeholder}); */
  background-color: #75757533;
  /* margin-top: -10px; */

  &.video,
  &.pdf {
    padding: 70px;
    background-color: #75757533;
    background-image: none;
  }

  &.video-thumbnail {
    background-color: #75757533;
    padding: 0px;
  }

  &.isPdf {
    padding: 4px;
  }
`

export const VideoCont = styled.div`
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`

export const StyledVideo = styled.video`
  max-width: 100%;
  border-radius: 8px;
  height: 450px;
  margin-top: 20px;
  width: 70vw;
`

export const IframeCont = styled(FlexRow)`
  iframe {
    width: 90vw;
    margin-top: 20px;
    height: calc(100vh - 140px);
  }
`

export const AllMediaCont = styled.div`
  overflow-x: hidden;
  min-height: calc(100vh - 114px);

  .media-filter {
    p {
      margin: 6px 0;
      font-family: ${Nue.medium};
    }

    .first {
      margin-top: 0;
    }
  }
`
