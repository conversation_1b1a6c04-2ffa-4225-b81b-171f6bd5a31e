import { Formik } from 'formik'
import React, { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import {
  createProjectType,
  deleteProjectType,
  getProjectTypeById,
  getProjectTypes,
  getTaskGroupsInUse,
  updateProjectType,
} from '../../../../logic/apis/projects'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import {
  generateUUID,
  getDataFromLocalStorage,
  isSuccess,
  notify,
  transformObjectValues,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import DraggableDiv from '../../../../shared/draggableDiv/DraggableDiv'
import {
  ColorCategory,
  countUnsavedChanges,
  pitchModConstant,
  ProjectTypeGroups,
  ProjectTypeQuestions,
} from './constant'
import Toggle from '../../../../shared/toggle/Toggle'
import { DeleteSvg, EditSvg } from '../../../../shared/helpers/images'
import { StorageKey } from '../../../../shared/helpers/constants'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { CommonColorModal } from './CommonColorModal'
import { AddGroup } from './AddGroup'
import { AddQuestions } from './AddQuestions'
import { SLoader } from '../../../../shared/components/loader/Loader'

const travelPersonDropdown: Record<number, string> = {
  0: 'None',
  1: '1 person',
  2: '2 people',
  3: '3 people',
  4: '4 people',
}
const travelPersonVal: Record<string, number> = {
  None: 0,
  '1 person': 1,
  '2 people': 2,
  '3 people': 3,
  '4 people': 4,
}

export interface IProjectType {
  name: string
  asbTestRequired: boolean
  permitRequired: boolean
  description: string
  typeMinimum: number
  markup: any
  deposit: any
  downPmt: any
  // package: string
  usesPitch: boolean
  createdBy: string
  _id: string
  colorCode?: string
  groups?: string[]
  group?: string
  pitchMod?: any
  minTravelPpl?: number
  salesCommision?: {
    commission: number
    jobSale: number
    jobStart: number
    jobCompletion: number
  }
}

const ProjectSchema = Yup.object().shape({
  salesCommision: Yup.object()
    .shape({
      commission: Yup.number().required('Required').max(100, 'commission cannot be greater than 100'),
      jobSale: Yup.number(),
      jobStart: Yup.number(),
      jobCompletion: Yup.number(),
    })
    .test(
      'total-sum',
      'The sum of % at Time of Sale, % at Job Start, and % at Job Completion should not be greater than 100',
      (values) => {
        const { jobSale = 0, jobStart = 0, jobCompletion = 0 } = values
        return jobSale + jobStart + jobCompletion <= 100
      }
    ),

  pitchMod: Yup.object().test('no-decimals', 'Invalid values in pitchMod.', (obj, context) => {
    const errors: Record<string, string> = {}

    for (const [key, value] of Object.entries(obj)) {
      if (value && !Number.isInteger(value)) {
        const lower = Math.floor(value)
        const upper = Math.ceil(value)
        errors[key] = `Enter either ${lower} or ${upper}`
      }
    }

    if (Object.keys(errors).length > 0) {
      return context.createError({
        path: 'pitchMod',
        message: errors,
      })
    }

    return true
  }),
})

let selectedQuestionsLocked = []
let selectedGroupsLocked = []
let colorCategoriesLocked = []
const ProjectTypeDetails = () => {
  const inputRef = useRef()
  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company
  const [selectedColor, setSelectedColor] = useState('#ff0000')
  const [usedGroups, setUsedGroups] = useState([])
  const [projectTypeName, setProjectTypeName] = useState('')
  const [colorModal, setColorModal] = useState(false)
  const [groupModal, setGroupModal] = useState(false)
  const [isEditGroup, setisEditGroup] = useState(false)
  const [isEditQuestions, setisEditQuestions] = useState(false)
  const [editGroupName, setEditGroupName] = useState('')
  const [editQuestionName, setEditQuestionName] = useState('')
  const [editCategoryName, setEditCategoryName] = useState('')
  const [editColorName, setEditColorName] = useState('')
  const [questionsModal, setQuestionsModal] = useState(false)
  const [selectedQuestions, setSelectedQuestions] = useState<ProjectTypeQuestions[]>([])
  const [selectedGroups, setSelectedGroups] = useState<ProjectTypeGroups[]>([])
  const [colorCategories, setColorCategories] = useState<ColorCategory[]>([])
  const [isColor, setIsColor] = useState(false)
  const [allUsedGroups, setAllUsedGroups] = useState([])
  const [changeCount, setChangeCount] = useState(0)
  const { projectTypeId } = useParams()
  const navigate = useNavigate()
  const [dataTypeById, setDataTypeById] = useState({})

  const [initValues, setInitValues] = useState({
    name: '',
    asbTestRequired: false,
    permitRequired: false,
    description: '',
    typeMinimum: '',
    usesPitch: false,
    markup: '',
    deposit: '',
    downPmt: '',
    minTravelPpl: 0,
    groups: [],
    group: '',
    pitchMod: pitchModConstant,
    salesCommision: {
      commission: '',
      jobSale: '',
      jobStart: '',
      jobCompletion: '',
    },
  })

  const removeField = (cd: any, idx: number, fields: Array<any>) => {
    let newField = [...fields.slice(0, idx), ...fields.slice(idx + 1)]
    cd([...newField])
  }
  const [loading, setLoading] = useState(false)
  const [loadingDel, setLoadingDel] = useState(false)
  // getNameFrom_Id

  useEffect(() => {
    if (allUsedGroups?.length && projectTypeId) {
      const groups = allUsedGroups?.find((v: any) => v?._id === projectTypeId)?.groups || []
      const groupNames: ProjectTypeGroups[] | undefined =
        dataTypeById?.groups ||
        []
          .filter((group: any) => groups?.includes(group?._id)) // Filter groups by matching _id
          ?.map((group: any) => group.name)
      setUsedGroups(groupNames)
    }
  }, [projectTypeId, allUsedGroups, dataTypeById?.groups?.length])

  useEffect(() => {
    if (projectTypeId) {
      const fetchProjectTypes = async () => {
        try {
          setLoading(true)
          const res = await getProjectTypeById(projectTypeId)
          if (isSuccess(res)) {
            const { projectType } = res.data.data
            setDataTypeById(projectType)
            selectedQuestionsLocked = projectType?.questions?.map((name: any) => ({ name }))
            selectedGroupsLocked = projectType?.groups
            colorCategoriesLocked = projectType?.priceColor
            setSelectedGroups(projectType?.groups || [])
            setColorCategories(projectType?.priceColor || [])
            setSelectedQuestions(projectType?.questions?.map((name: any) => ({ name })))
            setInitValues({
              name: projectType ? projectType.name : '',
              asbTestRequired: projectType ? projectType.asbTestRequired : false,
              permitRequired: projectType ? projectType.permitRequired : false,
              description: projectType ? projectType.description : '',
              typeMinimum: projectType ? projectType.typeMinimum : '',
              // packages: projectType ? (projectType.package === 'Yes' ? true : false) : false,
              usesPitch: projectType ? projectType.usesPitch : false,
              markup: projectType?.markup ? Math.round(projectType.markup * 100).toString() : '',
              deposit: projectType?.deposit ? Math.round(projectType.deposit * 100).toString() : '',
              downPmt: projectType?.downPmt ? Math.round(projectType.downPmt * 100).toString() : '',
              minTravelPpl: projectType ? travelPersonDropdown[projectType?.minTravelPpl!] : 0,
              groups: [],
              group: '',
              pitchMod: projectType?.pitchMod ? transformObjectValues(projectType?.pitchMod, true) : pitchModConstant,
              salesCommision: {
                commission: projectType ? Math.round(projectType?.salesCommision?.commission! * 100) : '',
                jobSale: projectType ? Math.round(projectType?.salesCommision?.jobSale! * 100) : '',
                jobStart: projectType ? Math.round(projectType?.salesCommision?.jobStart! * 100) : '',
                jobCompletion: projectType ? Math.round(projectType?.salesCommision?.jobCompletion! * 100) : '',
              },
            })
          } else throw new Error(res?.data?.message)
        } catch (err) {
          notify('Unable to fetch Project Type!', 'error')
          console.log('Project type create err', err)
        } finally {
          setLoading(false)
        }
      }
      fetchProjectTypes()
    }
  }, [projectTypeId])
  useEffect(() => {
    const initFetchTaskGroupsInUse = async () => {
      try {
        const res = await getTaskGroupsInUse({})
        if (isSuccess(res)) {
          console.log({ res })
          if (res?.data?.data?.uniqueGroups?.length) {
            setAllUsedGroups(res?.data?.data?.uniqueGroups)
          }
        }
      } catch (error) {
        console.log(error)
      }
    }
    initFetchTaskGroupsInUse()
  }, [])

  const onSubmit = async (values: typeof initValues) => {
    try {
      setLoading(true)

      const res = projectTypeId
        ? await updateProjectType({
            createdBy: currentMember._id,
            description: values.description,
            typeMinimum: Number(values?.typeMinimum) || 0,
            markup: Number(values.markup) / 100,
            deposit: Number(values.deposit) / 100,
            downPmt: Number(values.downPmt) / 100,
            name: values.name,
            asbTestRequired: values.asbTestRequired,
            permitRequired: values.permitRequired,
            projectTypeId: projectTypeId,
            colorCode: selectedColor,
            usesPitch: values?.usesPitch,
            minTravelPpl: travelPersonVal[values.minTravelPpl!],
            pitchMod: transformObjectValues(values?.pitchMod, false),
            groups: selectedGroups,
            questions: selectedQuestions?.map((v) => v?.name),
            priceColor: colorCategories,
            salesCommision: {
              commission: Number(values.salesCommision.commission) / 100,
              jobSale: Number(values.salesCommision.jobSale) / 100,
              jobStart: Number(values.salesCommision.jobStart) / 100,
              jobCompletion: Number(values.salesCommision.jobCompletion) / 100,
            },
          })
        : await createProjectType({
            createdBy: currentMember._id,
            description: values.description,
            typeMinimum: Number(values?.typeMinimum) || 0,
            markup: Number(values.markup) / 100,
            deposit: Number(values.deposit) / 100,
            downPmt: Number(values.downPmt) / 100,
            name: values.name,
            asbTestRequired: values.asbTestRequired,
            permitRequired: values.permitRequired,
            colorCode: selectedColor,
            usesPitch: values?.usesPitch,
            minTravelPpl: travelPersonVal[values.minTravelPpl!],
            pitchMod: transformObjectValues(values?.pitchMod, false),
            groups: [...selectedGroups, { name: 'None', _id: 'none' }],
            questions: selectedQuestions?.map((v) => v?.name),
            priceColor: colorCategories,
            salesCommision: {
              commission: Number(values.salesCommision.commission) / 100,
              jobSale: Number(values.salesCommision.jobSale) / 100,
              jobStart: Number(values.salesCommision.jobStart) / 100,
              jobCompletion: Number(values.salesCommision.jobCompletion) / 100,
            },
          })
      if (isSuccess(res)) {
        notify(`${projectTypeId ? 'Updated' : 'Created new'} Project Type!`, 'success')
        navigate(`/settings/project-types`)
        setLoading(false)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      setLoading(false)
      console.log('Project type create err', err)
    } finally {
      setLoading(false)
    }
  }

  const onDelete = async () => {
    try {
      setLoadingDel(true)
      const res = await deleteProjectType({
        id: projectTypeId ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted Project Type!`, 'success')
        navigate('/settings/project-types')
        setLoadingDel(false)
      }
    } catch (err: any) {
      notify(err?.message ?? 'Failed to delete Project Type!', 'error')
    } finally {
      setLoadingDel(false)
    }
  }

  const handleColorChange = (event: any) => {
    setSelectedColor(event.target.value)
  }

  return (
    <>
      {loading ? (
        <>
          <SLoader height={15} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={15} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={15} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
          <SLoader height={45} width={100} isPercent skeletonStyle={{ marginBottom: '10px' }} />
        </>
      ) : (
        <>
          <Formik
            initialValues={initValues}
            enableReinitialize={true}
            onSubmit={onSubmit}
            validationSchema={ProjectSchema}
            validateOnChange={true}
            validateOnBlur={false}
          >
            {({ values, errors, touched, resetForm, setFieldValue, handleSubmit }) => {
              useEffect(() => {
                setProjectTypeName(values.name)
              }, [values.name])

              useEffect(() => {
                const count = countUnsavedChanges(
                  initValues,
                  values,
                  {
                    selectedQuestions: selectedQuestionsLocked,
                    selectedGroups: selectedGroupsLocked,
                    colorCategories: colorCategoriesLocked,
                  },
                  {
                    selectedQuestions,
                    selectedGroups,
                    colorCategories,
                  }
                )

                setChangeCount(count)
              }, [values, initValues, selectedQuestions, selectedGroups, colorCategories])

              const scrollToFirstError = () => {
                const allErrors = errors || {} // Get all errors in the form

                const errorFields = Object.keys(allErrors) // Get all fields with errors

                // Check for errors within customData
                const userInputDataErrors = allErrors.pitchMod || {}
                const userInputDataFields = Object.keys(userInputDataErrors)

                for (let i = 0; i < userInputDataFields.length; i++) {
                  const field = `pitchMod.${userInputDataFields[i]}`
                  const element = document.querySelector(`[name="${field}"]`)

                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    return // Exit after scrolling to the first missing field within customData
                  }
                }

                // Check for errors outside customData
                for (let i = 0; i < errorFields.length; i++) {
                  const field = errorFields[i]
                  if (!field.startsWith('pitchMod.')) {
                    const element = document.querySelector(`[name="${field}"]`)

                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                      return // Exit after scrolling to the first missing field outside customData
                    }
                  }
                }
              }

              return (
                <Styled.ProjectWrapper>
                  <SharedStyled.FlexBox justifyContent="space-between">
                    <div className="header">
                      <SharedStyled.SectionTitle>
                        {projectTypeId ? 'Edit' : 'Add New'} Project Type
                      </SharedStyled.SectionTitle>
                    </div>
                    <div>
                      {projectTypeId ? (
                        <Button
                          maxWidth="fit-content"
                          className="delete"
                          type="button"
                          onClick={() => onDelete()}
                          isLoading={loadingDel}
                        >
                          Delete
                        </Button>
                      ) : null}
                    </div>
                  </SharedStyled.FlexBox>
                  <SharedStyled.FlexBox flexDirection="column" gap="10px">
                    <div className="scrollable-content">
                      <InputWithValidation
                        labelName="Name"
                        stateName="name"
                        value={values.name}
                        error={touched.name && errors.name ? true : false}
                        passRef={inputRef}
                      />
                      <InputWithValidation
                        labelName="Description"
                        stateName="description"
                        value={values.description}
                        error={touched.description && errors.description ? true : false}
                      />
                      <SharedStyled.FlexRow width="max-content" margin="10px 0 0 0">
                        <SharedStyled.ContentHeader>Client Payment schedule</SharedStyled.ContentHeader>
                      </SharedStyled.FlexRow>

                      <SharedStyled.FlexCol gap="0" padding="0 0 0 16px">
                        <InputWithValidation
                          labelName="Job Minimum"
                          stateName="typeMinimum"
                          value={values.typeMinimum}
                          error={touched.typeMinimum && errors.typeMinimum ? true : false}
                          inputType="number"
                        />
                        <InputWithValidation
                          labelName="Deposit %"
                          stateName="deposit"
                          value={values.deposit}
                          error={touched.deposit && errors.deposit ? true : false}
                          inputType="number"
                        />
                        <InputWithValidation
                          labelName="Down Payment %"
                          stateName="downPmt"
                          value={values.downPmt}
                          error={touched.downPmt && errors.downPmt ? true : false}
                          inputType="number"
                        />
                      </SharedStyled.FlexCol>

                      <SharedStyled.FlexRow width="max-content" margin="10px 0 0 0">
                        <SharedStyled.ContentHeader>Sales Commission schedule</SharedStyled.ContentHeader>
                      </SharedStyled.FlexRow>

                      {errors?.salesCommision && typeof errors?.salesCommision === 'string' && (
                        <SharedStyled.ErrorContainer>
                          <SharedStyled.ErrorMsg>{errors?.salesCommision}</SharedStyled.ErrorMsg>
                        </SharedStyled.ErrorContainer>
                      )}

                      <SharedStyled.FlexCol gap="0" padding="0 0 0 16px">
                        <InputWithValidation
                          labelName="Commission %"
                          stateName="salesCommision.commission"
                          value={values.salesCommision.commission}
                          error={errors.salesCommision?.commission ? true : false}
                          inputType="number"
                        />
                        <InputWithValidation
                          labelName="% at Time of Sale"
                          stateName="salesCommision.jobSale"
                          value={values.salesCommision.jobSale}
                          error={errors.salesCommision ? true : false}
                          inputType="number"
                        />
                        <InputWithValidation
                          labelName="% at Job Start"
                          stateName="salesCommision.jobStart"
                          value={values.salesCommision.jobStart}
                          error={errors.salesCommision ? true : false}
                          inputType="number"
                        />
                        <InputWithValidation
                          labelName="% at Job Completion"
                          stateName="salesCommision.jobCompletion"
                          value={values.salesCommision.jobCompletion}
                          error={errors.salesCommision ? true : false}
                          inputType="number"
                        />
                      </SharedStyled.FlexCol>
                      <InputWithValidation
                        labelName="Markup %"
                        stateName="markup"
                        value={values.markup}
                        error={touched.markup && errors.markup ? true : false}
                        inputType="number"
                      />

                      <CustomSelect
                        dropDownData={Object.values(travelPersonDropdown)}
                        setValue={() => {}}
                        stateName="minTravelPpl"
                        value={values.minTravelPpl}
                        error={touched.minTravelPpl && errors.minTravelPpl ? true : false}
                        setFieldValue={setFieldValue}
                        labelName="Minimum Travel Fee - 1 round trip for:"
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />

                      <SharedStyled.FlexCol gap="20px" margin="12px 0 0 0">
                        <Styled.OptionsWrapper>
                          <SharedStyled.FlexBox justifyContent="space-between" alignItems="center">
                            <div>
                              <b>Groups:</b>
                            </div>
                            <div>
                              <Button type="button" onClick={() => setGroupModal(true)}>
                                Add Group
                              </Button>
                            </div>
                          </SharedStyled.FlexBox>
                          <DraggableDiv
                            items={selectedGroups?.map((v) => v.name)}
                            setItems={(updatedGroups: ProjectTypeGroups[]) => setSelectedGroups(updatedGroups)}
                            // isModal
                            // horizontal
                            renderChild={(item, index) => (
                              <Styled.StepWrapper key={index}>
                                <div>
                                  {index + 1}. {item}
                                </div>
                                <div>
                                  <>
                                    {
                                      <span
                                        className="cross"
                                        onClick={() => {
                                          setisEditGroup(true)
                                          setGroupModal(true)
                                          setEditGroupName(item)
                                        }}
                                      >
                                        <img src={EditSvg} alt="delete icon" />
                                      </span>
                                    }
                                  </>
                                  &nbsp;
                                  <>
                                    {!usedGroups.includes(item) && (
                                      <span
                                        className="cross"
                                        onClick={() => {
                                          const confirmed = window.confirm('Would you like to remove?')
                                          if (confirmed) {
                                            removeField(
                                              (values: ProjectTypeGroups[]) => setSelectedGroups(values),
                                              index,
                                              selectedGroups
                                            )
                                          }
                                        }}
                                      >
                                        {/* <CrossIcon /> */}
                                        <img src={DeleteSvg} alt="delete icon" />
                                      </span>
                                    )}
                                  </>
                                </div>
                              </Styled.StepWrapper>
                            )}
                            // dragIndexRange={[0, selectedGroups.length - 1]}
                          />
                        </Styled.OptionsWrapper>
                      </SharedStyled.FlexCol>

                      <SharedStyled.FlexCol gap="20px" margin="12px 0 0 0">
                        <Styled.OptionsWrapper>
                          <SharedStyled.FlexBox justifyContent="space-between" alignItems="center">
                            <div>
                              <b>Questions:</b>
                            </div>
                            <div>
                              <Button type="button" onClick={() => setQuestionsModal(true)}>
                                Add Question
                              </Button>
                            </div>
                          </SharedStyled.FlexBox>

                          <DraggableDiv
                            items={selectedQuestions?.map((v) => v.name)}
                            setItems={(updatedQuestions: string[]) => setSelectedQuestions(updatedQuestions)}
                            // isObject
                            // horizontal
                            // id={1 || 0}
                            renderChild={(item, index) => (
                              <Styled.StepWrapper key={index}>
                                <div>
                                  {index + 1}. {item}
                                </div>
                                <div>
                                  <>
                                    {
                                      <span
                                        className="cross"
                                        onClick={() => {
                                          setisEditQuestions(true)
                                          setQuestionsModal(true)
                                          setEditQuestionName(item)
                                        }}
                                      >
                                        <img src={EditSvg} alt="delete icon" />
                                      </span>
                                    }
                                  </>
                                  &nbsp;
                                  <>
                                    {
                                      <span
                                        className="cross"
                                        onClick={() => {
                                          const confirmed = window.confirm('Would you like to remove?')
                                          if (confirmed) {
                                            removeField(
                                              (values: ProjectTypeGroups[]) => setSelectedQuestions(values),
                                              index,
                                              selectedQuestions
                                            )
                                          }
                                        }}
                                      >
                                        {/* <CrossIcon /> */}
                                        <img src={DeleteSvg} alt="delete icon" />
                                      </span>
                                    }
                                  </>
                                </div>
                              </Styled.StepWrapper>
                            )}
                            // dragIndexRange={[0, selectedGroups.length - 1]}
                          />
                        </Styled.OptionsWrapper>
                      </SharedStyled.FlexCol>

                      <Styled.ColorPickerInput type="color" value={selectedColor} onChange={handleColorChange} />
                      <div style={{ marginTop: '10px' }}>
                        <b>Selected Color: {selectedColor}</b>
                      </div>

                      {/* <Checkbox
                  title="Uses Pitch"
                  value={values.usesPitch}
                  onChange={() => setFieldValue('usesPitch', !values.usesPitch)}
                /> */}

                      <Toggle
                        title="Asbestos"
                        isToggled={values.asbTestRequired}
                        onToggle={() => {
                          setFieldValue('asbTestRequired', !values.asbTestRequired)
                        }}
                      />
                      <Toggle
                        title="Permit "
                        isToggled={values.permitRequired}
                        onToggle={() => {
                          setFieldValue('permitRequired', !values.permitRequired)
                        }}
                      />
                      <Toggle
                        title="Uses Pitch"
                        isToggled={values.usesPitch}
                        onToggle={() => {
                          setFieldValue('usesPitch', !values.usesPitch)
                        }}
                      />

                      {values.usesPitch && (
                        <>
                          <SharedStyled.SectionTitle>Pitch Modifier</SharedStyled.SectionTitle>
                          <SharedStyled.FlexBox column="column">
                            <div>
                              <>
                                {Object?.entries(values?.pitchMod)
                                  ?.slice(0, 10)
                                  ?.map(([key, value], index) => (
                                    <Styled.NameValueUnitContainer height="52px" key={index}>
                                      <SharedStyled.NameDiv minWidth="70px">{key}:</SharedStyled.NameDiv>
                                      <InputWithValidation
                                        labelName=""
                                        stateName={`pitchMod.${key}`}
                                        value={value}
                                        error={errors?.pitchMod?.[key] ? errors?.pitchMod?.[key] : false}
                                        inputType="number"
                                        maxWidth="120px"
                                        minWidth="120px"
                                        borderRadius="0"
                                        paddingInput="0"
                                      />
                                      <SharedStyled.UnitDiv>%</SharedStyled.UnitDiv>
                                    </Styled.NameValueUnitContainer>
                                  ))}
                              </>
                            </div>
                            <div>
                              <>
                                {Object?.entries(values?.pitchMod)
                                  ?.slice(10)
                                  ?.map(([key, value], index) => (
                                    <Styled.NameValueUnitContainer height="52px" key={index}>
                                      <SharedStyled.NameDiv minWidth="70px">{key}:</SharedStyled.NameDiv>
                                      <InputWithValidation
                                        labelName=""
                                        stateName={`pitchMod.${key}`}
                                        value={value}
                                        error={errors?.pitchMod?.[key] ? errors?.pitchMod?.[key] : false}
                                        inputType="number"
                                        maxWidth="120px"
                                        minWidth="120px"
                                        borderRadius="0"
                                        paddingInput="0"
                                      />
                                      {/* <SharedStyled.ValueInput
                                  width="120px"
                                  type="number"
                                  value={value}
                                  // onChange={onChangePitchModifierValue(index1, index)}
                                /> */}
                                      <SharedStyled.UnitDiv>%</SharedStyled.UnitDiv>
                                    </Styled.NameValueUnitContainer>
                                  ))}
                              </>
                            </div>
                          </SharedStyled.FlexBox>
                        </>
                      )}

                      <SharedStyled.FlexBox justifyContent="space-between" alignItems="center">
                        <div>
                          <b>Color Categories:</b>
                        </div>
                        <div>
                          <Button
                            type="button"
                            onClick={() => {
                              setColorModal(true)
                              setIsColor(false)
                            }}
                          >
                            Add Color Categories
                          </Button>
                        </div>
                      </SharedStyled.FlexBox>

                      <div>
                        {colorCategories?.map((values: any, index: number) => (
                          <>
                            <Styled.StepWrapper key={index}>
                              <div>
                                {index + 1}. {values.name}
                              </div>
                              <div>
                                <>
                                  {
                                    <span
                                      className="cross"
                                      onClick={() => {
                                        setisEditGroup(true)
                                        setColorModal(true)
                                        setEditCategoryName(values.name)
                                      }}
                                    >
                                      <img src={EditSvg} alt="edit icon" />
                                    </span>
                                  }
                                </>
                                &nbsp;
                                <>
                                  {
                                    <span
                                      className="cross"
                                      onClick={() => {
                                        removeField((values: any) => setColorCategories(values), index, colorCategories)
                                      }}
                                    >
                                      <img src={DeleteSvg} alt="delete icon" />
                                    </span>
                                  }
                                </>
                                &nbsp;
                              </div>
                            </Styled.StepWrapper>

                            <>
                              <SharedStyled.FlexBox justifyContent="space-between" alignItems="center">
                                <div>
                                  <SharedStyled.Text
                                    fontWeight="600"
                                    fontSize="16px"
                                    width="100%"
                                    textAlign="flex-start"
                                  >
                                    {`${values.name} `}
                                  </SharedStyled.Text>
                                </div>
                                <div>
                                  <SharedStyled.Button
                                    maxWidth="30px"
                                    marginTop="7px"
                                    mediaHeight="30px"
                                    type="button"
                                    onClick={() => {
                                      setColorModal(true)
                                      setIsColor(true)
                                      setEditCategoryName(values.name)
                                    }}
                                  >
                                    <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                                  </SharedStyled.Button>
                                </div>
                              </SharedStyled.FlexBox>

                              <Styled.OptionsWrapper1>
                                {values?.colors?.map((field, idx: number) => (
                                  <div key={idx} className="option">
                                    <span
                                      onClick={() => {
                                        setEditCategoryName(values.name)
                                        setIsColor(true)
                                        setColorModal(true)
                                        setEditColorName(field)
                                        setisEditGroup(true)
                                      }}
                                    >
                                      {field}
                                    </span>
                                    <span
                                      onClick={() => {
                                        setColorCategories((prevCategories: any) => {
                                          return prevCategories?.map((v: any) => {
                                            if (v.name === values.name) {
                                              return { ...v, colors: v.colors.filter((v) => v !== field) }
                                            }
                                            return v
                                          })
                                        })
                                      }}
                                    >
                                      <CrossIcon />
                                    </span>
                                  </div>
                                ))}
                              </Styled.OptionsWrapper1>
                            </>
                          </>
                        ))}
                      </div>
                    </div>
                  </SharedStyled.FlexBox>
                  <Styled.FixedSaveButtonContainer className="footer">
                    {changeCount > 0 && projectTypeId ? (
                      <SharedStyled.Text fontSize="18px" fontWeight="700" color="red">
                        {changeCount} Unsaved Change{changeCount > 1 ? 's' : ''}
                      </SharedStyled.Text>
                    ) : null}

                    <Button
                      type="button"
                      maxWidth="fit-content"
                      onClick={() => {
                        scrollToFirstError()
                        handleSubmit()
                      }}
                      isLoading={loading}
                    >
                      Save
                    </Button>

                    <Button
                      className="gray"
                      maxWidth="fit-content"
                      onClick={() => {
                        navigate('/settings/project-types')
                      }}
                      isLoading={loading}
                    >
                      Cancel
                    </Button>
                  </Styled.FixedSaveButtonContainer>
                </Styled.ProjectWrapper>
              )
            }}
          </Formik>
        </>
      )}

      <CustomModal show={colorModal}>
        <CommonColorModal
          onClose={() => {
            setColorModal(false)
            setEditColorName('')
            setEditCategoryName('')
            setIsColor(false)
            setisEditGroup(false)
          }}
          setColorCategories={setColorCategories}
          setEditCategoryName={setEditCategoryName}
          editCategoryName={editCategoryName}
          isEdit={isEditGroup}
          projectTypeName={projectTypeName}
          isColor={isColor}
          editColorName={editColorName}
          setEditColorName={setEditColorName}
          setIsColor={setIsColor}
          setisEditGroup={setisEditGroup}
        />
      </CustomModal>

      <CustomModal show={groupModal}>
        <AddGroup
          onClose={() => {
            setGroupModal(false)
            // setEditReferrerVals(null)
            setisEditGroup(false)
          }}
          setSelectedGroups={setSelectedGroups}
          setEditGroupName={setEditGroupName}
          isEdit={isEditGroup}
          editGroupName={editGroupName}
          projectTypeName={projectTypeName}
        />
      </CustomModal>
      <CustomModal show={questionsModal}>
        <AddQuestions
          onClose={() => {
            setQuestionsModal(false)
            setisEditQuestions(false)
            // setEditReferrerVals(null)
          }}
          isEdit={isEditQuestions}
          setSelectedQuestions={setSelectedQuestions}
          setEditQuestionName={setEditQuestionName}
          editQuestionName={editQuestionName}
          projectTypeName={projectTypeName}
        />
      </CustomModal>
    </>
  )
}

export default ProjectTypeDetails
