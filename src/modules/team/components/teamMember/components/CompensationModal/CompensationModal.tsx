import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../../../assets/newIcons/unitModal.svg'
import {
  createCompensation,
  getCompensation,
  inviteCompensation,
  updateCompensation,
} from '../../../../../../logic/apis/compensation'
import { getPaySchedule } from '../../../../../../logic/apis/paySchedule'
import { getPosition } from '../../../../../../logic/apis/position'
import Button from '../../../../../../shared/components/button/Button'
import CustomSelect from '../../../../../../shared/customSelect/CustomSelect'
import {
  PER,
  PERIOD_OBJ,
  PERIOD_OBJ1,
  PER_OBJ,
  PER_OBJ1,
  StorageKey,
  SubscriptionPlanType,
} from '../../../../../../shared/helpers/constants'
import { onlyNumber, twoDecimal } from '../../../../../../shared/helpers/regex'
import {
  dayjsFormat,
  daysInMonth,
  getDataFromLocalStorage,
  isCustomTruthy,
  isSuccess,
  notify,
  resetDateToUTC,
  startOfDate,
} from '../../../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../../../styles/styled'
import { colors } from '../../../../../../styles/theme'
import * as Styled from './style'
import { CheckDivContainer } from '../../../../../taskSettings/components/createTaskPopUp/style'
import Toggle from '../../../../../../shared/toggle/Toggle'
import { SharedDate } from '../../../../../../shared/date/SharedDate'
import useFetch from '../../../../../../logic/apis/useFetch'
import { getDepartments } from '../../../../../../logic/apis/department'
import { getTeamMembers } from '../../../../../../logic/apis/team'
import { getSubContractors } from '../../../../../../logic/apis/subcontractor'
import { updateCompanyMemberInfo } from '../../../../../../logic/apis/company'

/**
 * I_CompensationModal is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CompensationModal {
  setCompensationModal: React.Dispatch<React.SetStateAction<boolean>>
  noData: boolean
  setDataUpdated: React.Dispatch<React.SetStateAction<boolean>>
  isEdit: boolean
  compensationData: any
  hireDateCompensation: string
  pwVersions?: any[]
  commissionPosition?: string[]
  pwPosition?: string[]
  isEffectivePayPeriodOver?: boolean
  isInviteModal?: boolean
  selectedInviteId?: string
  setInviteUpdate?: React.Dispatch<React.SetStateAction<boolean>>
  isSubcontractorPositionProp?: boolean
  subContractorId?: string
  isFromTeamMemberSection?: boolean
}

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  position: string
  pieceWorkHourlyRate: string
  salesCommision: string
  selfLead?: string
  useBenchmarkBonus?: boolean
  wage: number | string
  per: string
  paySchedule: string
  effectivePayPeriod: string
  ownPieceWork: string | number
  crewPieceWork: string | number
  reasonForChange: string
  versionId?: string
  hireDate?: string
  department?: string
  manager?: string
  subContractorId?: string
}

function filterDatesBySelectedDate(dates: string[], selectedDate: string) {
  const selected = new Date(resetDateToUTC(selectedDate))
  const selectedTimestamp = selected.getTime()

  const result = dates.filter((range) => {
    const [startStr, endStr] = range?.split(' - ')

    const start = new Date(resetDateToUTC(startStr))
    const end = new Date(resetDateToUTC(endStr))

    return selectedTimestamp >= start.getTime() && selectedTimestamp <= end.getTime()
  })

  return result.length ? result : ''
}

export const CompensationModal = (props: I_CompensationModal) => {
  const {
    setCompensationModal,
    pwVersions,
    commissionPosition,
    setDataUpdated,
    isEdit,
    compensationData,
    hireDateCompensation,
    pwPosition,
    isEffectivePayPeriodOver,
    isInviteModal,
    selectedInviteId,
    setInviteUpdate,
    isSubcontractorPositionProp,
    subContractorId,
    isFromTeamMemberSection,
  } = props

  const isAddModal = !isInviteModal && !isEdit
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    position: '',
    pieceWorkHourlyRate: '',
    salesCommision: '',
    wage: 0,
    per: '',
    paySchedule: '',
    effectivePayPeriod: '',
    ownPieceWork: '',
    crewPieceWork: '',
    reasonForChange: isAddModal ? 'Starting' : '',
    versionId: '',
    useBenchmarkBonus: false,
    hireDate: isAddModal || !isEffectivePayPeriodOver ? hireDateCompensation : '',
    department: '',
    manager: '',
    subContractorId: '',
  })
  const [formValues, setFormValues] = useState<any>({})
  const [positionData, setPositionData] = useState<any>([])
  const [positionIdData, setPositionIdData] = useState<any>({})
  const [effectivePayPeriodData, setEffectivePayPeriodData] = useState<any>([])
  const [effectivePayPeriodWithPayDay, setEffectivePayPeriodWithPayDay] = useState<any>({})
  const [payScheduleData, setPayScheduleData] = useState<any>([])
  const [payScheduleIdData, setPayScheduleIdData] = useState<any>({})
  const [payScheduleDetail, setPayScheduleDetail] = useState<any>([])
  const [noData, setNoData] = useState<boolean>(false)
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(!isInviteModal ? true : false)
  const [allPositionData, setAllPositionData] = useState([])
  const [isSubContractorPosition, setIsSubContractorPosition] = useState(false)
  const [allSubcontractorData, setAllSubcontractorData] = useState([])
  const [subcontractorDataFetched, setSubcontractorDataFetched] = useState(false)
  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  let { memberId, managerId } = useParams()

  const { data: departmentsData } = useFetch({
    fetchFn: () => isInviteModal && getDepartments({ deleted: false }, false),
  })
  const { data: teamsData } = useFetch({
    fetchFn: () => isInviteModal && getTeamMembers({ deleted: false, limit: 1000 }),
  })

  const managerData = teamsData?.memberData

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  /**
   * CompensationModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const CompensationModalSchema = Yup.object().shape({
    position: Yup.string().required('Required'),
    salesCommision: Yup.string().when('position', {
      is: (value: string) => {
        return commissionPosition?.includes(positionIdData[value])
      },
      then: Yup.string().required('Field is required'),
    }),

    selfLead: Yup.string().when('position', {
      is: (value: string) => {
        return commissionPosition?.includes(positionIdData[value])
      },
      then: Yup.string().required('Field is required'),
    }),

    wage: Yup.string()
      .matches(twoDecimal, 'Only numbers with upto two decimals are allowed')
      .when('position', {
        is: (_value: string) => !isSubContractorPosition,
        then: Yup.string().required('Field is required'),
      }),
    per: Yup.string().when('position', {
      is: (_value: string) => !isSubContractorPosition,
      then: Yup.string().required('Field is required'),
    }),
    paySchedule: Yup.string().when('position', {
      is: (_value: string) => !isSubContractorPosition,
      then: Yup.string().required('Field is required'),
    }),
    effectivePayPeriod: Yup.string().when('position', {
      is: (_value: string) => !isSubContractorPosition,
      then: Yup.string().required('Field is required'),
    }),
    reasonForChange: Yup.string().when('position', {
      is: (_value: string) => !isInviteModal,
      then: Yup.string().required('Field is required'),
    }),
    hireDate: Yup.string().when('position', {
      is: (_value: string) => isInviteModal,
      then: Yup.string().required('Field is required'),
    }),
    pieceWorkHourlyRate: Yup.string()
      .nullable()
      .when('position', {
        is: (value: string) => pwPosition?.includes(positionIdData[value]),
        then: Yup.string().required('Field is required'),
      }),
    versionId: Yup.string()
      .nullable()
      .when('position', {
        is: (value: string) => pwPosition?.includes(positionIdData[value]),
        then: Yup.string().required('Field is required'),
      }),
    department: Yup.string()
      .nullable()
      .when('position', {
        is: (_value: string) => isInviteModal && !isSubContractorPosition,
        then: Yup.string().required('Field is required'),
      }),
    manager: Yup.string()
      .nullable()
      .when('position', {
        is: (_value: string) => isInviteModal && !isSubContractorPosition,
        then: Yup.string().required('Field is required'),
      }),

    ownPieceWork: Yup.string()
      .nullable()
      .when('position', {
        is: (value: string) => value === 'Foreman',
        then: Yup.string().required('Field is required'),
      }),
    crewPieceWork: Yup.string()
      .nullable()
      .when('position', {
        is: (value: string) => value === 'Foreman',
        then: Yup.string().required('Field is required'),
      }),
  })

  useEffect(() => {
    if (isInviteModal) {
      setInitialValues({
        ...initialValues,

        position:
          Object?.keys(positionIdData)?.find((itm) => positionIdData?.[itm] === compensationData?.positionId) || '',
        wage: compensationData?.wageAmount ? Number(compensationData?.wageAmount)?.toFixed(2) : '',
        per: PER_OBJ[compensationData?.wageInterval] || '',
        paySchedule:
          Object?.keys(payScheduleIdData)?.find(
            (itm) => payScheduleIdData?.[itm] === compensationData?.payScheduleId
          ) || '',
        effectivePayPeriod: compensationData?.effectivePaySelected?.trim() || '',

        ownPieceWork: compensationData?.ownPieceWork ? Math.round(compensationData?.ownPieceWork * 100) : '',
        crewPieceWork: compensationData?.crewPieceWork ? Math.round(compensationData?.crewPieceWork * 100) : '',
        salesCommision: compensationData?.saleCommission
          ? `${Math.round(compensationData?.saleCommission * 100)}%`
          : '',
        selfLead: compensationData?.selfLead ? `${Math.round(compensationData?.selfLead * 100)}%` : '',
        useBenchmarkBonus: isProPlusPlan ? compensationData?.useBenchmarkBonus : undefined,
        manager: managerData?.find((manager: any) => manager?._id === compensationData?.managerId)?.name || '', // NEW
        department:
          departmentsData?.department?.find((dept: any) => dept?._id === compensationData?.departmentId)?.name || '', // NEW

        pieceWorkHourlyRate: compensationData?.pieceWorkHourlyRate,
        reasonForChange: compensationData?.reason,
        versionId: pwVersions?.find((itm) => itm?._id === compensationData?.versionId)?.name,
        hireDate: hireDateCompensation || '',
      })
    }
  }, [isInviteModal, positionIdData, payScheduleIdData, managerData, departmentsData])

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)

      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      let memId: any = memberId

      let dataObj = {
        memberId: memId,
        positionId: positionIdData[submittedValues.position],
        payScheduleId: payScheduleIdData[submittedValues.paySchedule],
        effectivePayPeriod: isSubContractorPosition
          ? new Date()?.toISOString()
          : effectivePayPeriodData[submittedValues.effectivePayPeriod]?.toISOString(),
        payDay: effectivePayPeriodWithPayDay[submittedValues.effectivePayPeriod]?.toISOString(),
        effectivePaySelected: submittedValues.effectivePayPeriod?.trim(),
        ownPieceWork: submittedValues.position === 'Foreman' ? Number(submittedValues.ownPieceWork) / 100 : undefined,
        crewPieceWork: submittedValues.position === 'Foreman' ? Number(submittedValues.crewPieceWork) / 100 : undefined,
        createdBy: currentMember._id,
        wageAmount: Number(submittedValues.wage),
        wageInterval: Number(PER_OBJ1[submittedValues.per]),
        saleCommission: commissionPosition?.includes(positionIdData[submittedValues?.position])
          ? submittedValues.salesCommision
            ? Number(submittedValues.salesCommision.replace(/[%]/g, '')) / 100
            : undefined
          : undefined,
        selfLead: commissionPosition?.includes(positionIdData[submittedValues?.position])
          ? submittedValues.selfLead
            ? Number(submittedValues.selfLead.replace(/[%]/g, '')) / 100
            : undefined
          : undefined,
        useBenchmarkBonus: commissionPosition?.includes(positionIdData[submittedValues?.position])
          ? submittedValues?.useBenchmarkBonus
          : false,
        pieceWorkHourlyRate: isCustomTruthy(submittedValues.pieceWorkHourlyRate)
          ? Number(submittedValues.pieceWorkHourlyRate)
          : undefined,
        reason: isInviteModal ? 'Starting Wage' : submittedValues.reasonForChange,
        versionId: pwVersions?.find((pw) => pw?.name === submittedValues?.versionId)?._id,
        managerId: managerData?.find((manager: any) => manager?.name === submittedValues?.manager)?._id, // NEW
        departmentId: departmentsData?.department?.find((dept: any) => dept?.name === submittedValues?.department)?._id, // NEW
        hireDate: isInviteModal ? startOfDate(submittedValues.hireDate) : undefined,
        subContractorId: isSubContractorPosition
          ? allSubcontractorData?.find((itm: any) => itm?.name === submittedValues?.subContractorId)?._id
          : undefined,
      }

      if (isSubContractorPosition && isFromTeamMemberSection) {
        await updateCompanyMemberInfo({
          subContractorId: isSubContractorPosition
            ? allSubcontractorData?.find((itm: any) => itm?.name === submittedValues?.subContractorId)?._id
            : undefined,
          memberId: memId,
        })
      }

      if (isInviteModal) {
        try {
          const response = await inviteCompensation(dataObj, selectedInviteId!)

          if (isSuccess(response)) {
            notify('Compensation Updated', 'success')
            resetForm?.()
          }
        } catch (error) {
          console.log('error====>', error)
        } finally {
          setLoading(false)
          setCompensationModal(false)
          setInviteUpdate?.((p) => !p)
        }
      } else {
        if (noData) {
          const response = await createCompensation(dataObj)
          if (response?.data?.statusCode === 201) {
            if (isEdit) {
              notify('You have successfully edited the compensation', 'success')
            } else {
              notify('You have successfully added the compensation', 'success')
            }
            setLoading(false)
            setCompensationModal(false)
            setDataUpdated((prev) => !prev)
            getCompensationInfo()
            if (!isEdit) {
              resetForm()
            }
          } else {
            notify(response?.data?.message, 'error')
            setLoading(false)
          }
        } else {
          const response = await updateCompensation(dataObj)
          if (response?.data?.statusCode === 200) {
            notify('You have successfully edited the compensation', 'success')
            setLoading(false)
            setCompensationModal(false)
            setDataUpdated((prev) => !prev)
            getCompensationInfo()
          } else {
            // notify(response?.data?.message, 'error')
            setLoading(false)
          }
        }
      }
      // }
    } catch (error) {
      console.error('CompensationModal handleSubmit', error)
      setLoading(false)
    }
  }

  const getPositionInfo = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getPosition({ deleted: false, limit: 100 }, false)

      if (response?.data?.statusCode === 200) {
        let position = response?.data?.data?.position
        let positionObj: any = []
        let positionObj2: any = {}

        position.forEach((pos: any, index: number) => {
          positionObj.push(pos.position)
          positionObj2 = { ...positionObj2, [pos.position]: pos._id }
        })
        positionObj = isInviteModal ? positionObj?.filter((itm: any) => itm !== 'CEO') : positionObj
        setAllPositionData(position)
        setPositionData(positionObj)
        setPositionIdData(positionObj2)
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getPositionInfo error', error)
    }
  }

  const getSubcontractorInfo = async () => {
    try {
      const subResponse = await getSubContractors({ deleted: false, retired: false, limit: '200', active: true })
      setAllSubcontractorData(subResponse?.data?.data?.subcontractor)
      setSubcontractorDataFetched(true)
    } catch (error) {
      console.error('getSubcontractorInfo error', error)
    }
  }

  const getCompensationInfo = async () => {
    try {
      setShimmerLoading(true)
      let memId: any = memberId
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getCompensation({ memberId: memId })

      if (response?.data?.statusCode === 200) {
        let compensation = response?.data?.data?.compensation ?? {}

        if (Object.values(compensation)?.length) {
          if (isEdit) {
            setInitialValues({
              ...initialValues,
              position: compensation?.position?.position,
              wage: Number(compensation?.wageAmount)?.toFixed(2),
              per: PER_OBJ[compensation?.wageInterval],
              paySchedule: compensation?.paySchedule?.name,
              effectivePayPeriod: isEffectivePayPeriodOver ? '' : compensation?.effectivePaySelected,

              ownPieceWork: compensation?.ownPieceWork ? Math.round(compensation?.ownPieceWork * 100) : '',
              crewPieceWork: compensation?.crewPieceWork ? Math.round(compensation?.crewPieceWork * 100) : '',
              salesCommision: compensation?.saleCommission ? `${Math.round(compensation?.saleCommission * 100)}%` : '',
              selfLead: compensation?.selfLead ? `${Math.round(compensation?.selfLead * 100)}%` : '',
              useBenchmarkBonus: compensation?.useBenchmarkBonus,
              subContractorId: subContractorId
                ? allSubcontractorData?.find((itm: any) => itm?._id === subContractorId)?.name
                : compensation?.subContractorId
                ? allSubcontractorData?.find((itm: any) => itm?._id === compensation?.subContractorId)?.name
                : '',
              pieceWorkHourlyRate: compensation?.pieceWorkHourlyRate,
              reasonForChange: isEffectivePayPeriodOver ? '' : compensation?.reason,
              versionId: pwVersions?.find((itm) => itm?._id === compensation?.versionId)?.name,
            })
          } else {
            setInitialValues({
              position: '',
              pieceWorkHourlyRate: '',
              salesCommision: '',
              wage: 0,
              per: '',
              paySchedule: '',
              effectivePayPeriod: '',
              ownPieceWork: '',
              crewPieceWork: '',
              reasonForChange: '',
            })
          }
          setShimmerLoading(false)
          setNoData(false)
          getNextPayPeriodsAndPayDays()
        } else {
          setNoData(true)
          setShimmerLoading(false)
        }
      } else {
        notify(response?.data?.message, 'error')
        setShimmerLoading(false)
      }
      // }
    } catch (error) {
      console.error('getCompensationInfo error', error)
      setShimmerLoading(false)
    }
  }

  const getPaySceduleInfo = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getPaySchedule({ deleted: false })
      if (response?.data?.statusCode === 200) {
        let payScheduleArray: any = []
        let payScheduleObj: any = {}
        let payScheduleNamePeriodObj: any = {}
        let paySchedule = response?.data?.data?.paySchedule

        paySchedule.forEach((pay: any) => {
          payScheduleArray.push(pay.name)
          payScheduleObj = { ...payScheduleObj, [pay.name]: pay._id }
          payScheduleNamePeriodObj = {
            ...payScheduleNamePeriodObj,
            [pay.name]: {
              period: PERIOD_OBJ1[pay.period],
              periodEnd: pay.payPeriodEndsOn
                ? new Date(pay.payPeriodEndsOn).toISOString().split('-').join('-').split('T')[0]
                : null,
              payDay: pay.paydayOn,
              periodEnd2: pay.payPeriodEndsOn2
                ? new Date(pay.payPeriodEndsOn2).toISOString().split('-').join('-').split('T')[0]
                : null,
              payDay2: pay.paydayOn2,
            },
          }
        })
        setPayScheduleData(payScheduleArray)
        setPayScheduleIdData(payScheduleObj)
        setPayScheduleDetail(payScheduleNamePeriodObj)
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getPositionInfo error', error)
    }
  }

  const getNextPayPeriodsAndPayDays = async () => {
    try {
      if (Object.keys(formValues).length > 0 && formValues.paySchedule !== '') {
        let today = new Date()
        today.setMonth(today.getMonth() + 2)

        const perio = payScheduleDetail[formValues.paySchedule]?.period
        let payPeriod = Number(PERIOD_OBJ[perio])

        let payEnd = payScheduleDetail[formValues?.paySchedule]?.periodEnd
        let payDay = payScheduleDetail[formValues.paySchedule]?.payDay
        let payEnd2 = payScheduleDetail[formValues.paySchedule]?.periodEnd2
        let payDay2 = payScheduleDetail[formValues?.paySchedule]?.payDay2

        // ========================================= New =========================================

        const data = getPayPeriods2NHR(
          PERIOD_OBJ1[payPeriod],
          new Date(payEnd).toISOString().split('-').join('-').split('T')[0],
          typeof payDay === 'number' ? payDay : new Date(payDay).toISOString().split('-').join('-').split('T')[0],
          payEnd2 ? new Date(payEnd2).toISOString().split('-').join('-').split('T')[0] : 0,
          payDay2 ?? 0
        )

        const res: Record<string, string> = {}
        const payPeriodData: Record<string, string> = {}

        data?.forEach((v: any) => {
          const key = `${dayjsFormat(v?.periodStart, 'M/D/YYYY')} - ${dayjsFormat(v?.periodEnd, 'M/D/YYYY')}`
          payPeriodData[key] = v.periodPayday
          res[key] = v.periodStart
        })

        setEffectivePayPeriodWithPayDay(payPeriodData)

        if (isEdit) {
          const filterDates = data
            ?.filter((itm: any) => new Date(itm?.periodPayday)?.getTime() >= new Date()?.getTime())
            ?.sort((a: any, b: any) => new Date(a.periodPayday)?.getTime() - new Date(b?.periodPayday)?.getTime())

          const newRes: Record<string, string> = {}
          filterDates?.forEach((v: any) => {
            const key = `${dayjsFormat(v?.periodStart, 'M/D/YYYY')} - ${dayjsFormat(v?.periodEnd, 'M/D/YYYY')}`
            newRes[key] = v.periodStart
          })

          setEffectivePayPeriodData(newRes)
          return
        }

        setEffectivePayPeriodData(res)

        // ========================================= New =========================================
      }
    } catch (error) {
      console.error('getNextPayPeriodsAndPayDays error', error)
    }
  }

  useEffect(() => {
    if (currentCompany && currentCompany._id) {
      getPositionInfo()
      getPaySceduleInfo()
      getSubcontractorInfo()
    }
  }, [currentCompany])

  useEffect(() => {
    if (!isInviteModal && subcontractorDataFetched) {
      getCompensationInfo()
    }
  }, [isEdit, currentCompany, isInviteModal, subcontractorDataFetched])

  useEffect(() => {
    if (formValues?.paySchedule) getNextPayPeriodsAndPayDays()
  }, [formValues])

  return (
    <>
      <Styled.CompensationContainer>
        <Formik
          initialValues={initialValues}
          enableReinitialize={true}
          onSubmit={handleSubmit}
          validationSchema={CompensationModalSchema}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors, touched, setFieldValue, dirty }) => {
            setFormValues(values)

            useEffect(() => {
              if (values?.position) {
                const contractPosition = allPositionData?.find((itm: any) => itm?.position === values?.position)
                setIsSubContractorPosition(contractPosition?.symbol === 'subContractor')
              }

              if (isSubcontractorPositionProp && allPositionData?.length) {
                const contractorName = allPositionData?.find((itm: any) => itm?.symbol === 'subContractor')?.position
                setFieldValue('position', contractorName)
              }
            }, [values?.position, isSubcontractorPositionProp, allPositionData?.length])

            useEffect(() => {
              if (isInviteModal && values?.paySchedule) {
                setFieldValue(
                  'effectivePayPeriod',
                  filterDatesBySelectedDate(Object.keys(effectivePayPeriodData), values?.hireDate!)?.[0]
                )
              }
            }, [isInviteModal, values?.paySchedule, effectivePayPeriodData, values?.hireDate])

            useEffect(() => {
              if (isAddModal && values?.paySchedule) {
                setFieldValue(
                  'effectivePayPeriod',
                  filterDatesBySelectedDate(Object.keys(effectivePayPeriodData), values?.hireDate!)?.[0]
                )
              }
            }, [isInviteModal, values?.paySchedule, effectivePayPeriodData, values?.hireDate])

            const hasPieceworkVersion = !!(
              pwVersions?.length && !!pwPosition?.includes(positionIdData[values?.position])
            )
            const hasSalesCommission = !!commissionPosition?.includes(positionIdData[values?.position])

            return (
              <>
                <SharedStyled.FlexCol alignItems="center">
                  <Styled.ModalHeaderContainer style={{ width: '100%' }}>
                    <SharedStyled.FlexRow>
                      <img src={UnitSvg} alt="modal icon" />
                      <SharedStyled.FlexCol alignItems={isInviteModal ? 'center' : 'flex-start'}>
                        <Styled.ModalHeader>{isInviteModal ? 'Invite New Member' : 'Compensation'}</Styled.ModalHeader>
                      </SharedStyled.FlexCol>
                    </SharedStyled.FlexRow>
                    <Styled.CrossContainer
                      onClick={() => {
                        setCompensationModal(false)
                      }}
                    >
                      <CrossIcon />
                    </Styled.CrossContainer>
                  </Styled.ModalHeaderContainer>
                  {isInviteModal ? <SharedStyled.Text fontSize="18px">Step 2</SharedStyled.Text> : null}
                </SharedStyled.FlexCol>

                {shimmerLoading ? (
                  <SharedStyled.FlexBox
                    padding="0px 20px 20px 20px"
                    width="100%"
                    justifyContent="center"
                    alignItems="center"
                    flexDirection="column"
                  >
                    <SharedStyled.Skeleton custWidth="100%" custHeight="50px" />
                    <SharedStyled.Skeleton custWidth="100%" custHeight="100px" custMarginTop="10px" />
                    <SharedStyled.Skeleton custWidth="100%" custHeight="100px" custMarginTop="10px" />
                    <SharedStyled.Skeleton custWidth="100%" custHeight="100px" custMarginTop="10px" />
                  </SharedStyled.FlexBox>
                ) : (
                  <SharedStyled.SettingModalContentContainer>
                    <Form className="form">
                      <SharedStyled.Content width="100%" disableBoxShadow={true} noPadding={true}>
                        {isInviteModal ? (
                          <SharedDate
                            value={values.hireDate}
                            labelName="Hire Date"
                            stateName="hireDate"
                            setFieldValue={setFieldValue}
                            error={touched.hireDate && errors.hireDate ? true : false}
                          />
                        ) : null}
                        <CustomSelect
                          value={values.position}
                          labelName="Position"
                          stateName="position"
                          dropDownData={positionData}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          disabled={isSubcontractorPositionProp}
                          margin="10px 0 0 0"
                          error={touched.position && errors.position ? true : false}
                        />
                        {isSubContractorPosition ? (
                          <SharedStyled.FlexCol>
                            <CustomSelect
                              value={values.subContractorId}
                              labelName="Sub contractor"
                              stateName="subContractorId"
                              dropDownData={allSubcontractorData?.map((itm: any) => itm?.name)}
                              setFieldValue={setFieldValue}
                              setValue={() => {}}
                              margin="10px 0 0 0"
                              error={touched.subContractorId && errors.subContractorId ? true : false}
                            />
                          </SharedStyled.FlexCol>
                        ) : (
                          <>
                            <CustomSelect
                              value={values.paySchedule}
                              labelName="Pay Schedule"
                              stateName="paySchedule"
                              dropDownData={payScheduleData}
                              setFieldValue={setFieldValue}
                              setValue={() => {}}
                              margin="10px 0 0 0"
                              error={touched.paySchedule && errors.paySchedule ? true : false}
                            />

                            <CustomSelect
                              value={values.effectivePayPeriod}
                              labelName="Effective Pay Period"
                              stateName="effectivePayPeriod"
                              dropDownData={Object.keys(effectivePayPeriodData) ?? ''}
                              setFieldValue={setFieldValue}
                              setValue={() => {}}
                              margin="10px 0 0 0"
                              error={touched.effectivePayPeriod && errors.effectivePayPeriod ? true : false}
                              showInitialValue
                              disabled={isInviteModal || isAddModal}
                            />

                            <SharedStyled.TwoInputDiv>
                              <InputWithValidation
                                labelName="$ Wage"
                                stateName="wage"
                                twoInput={true}
                                error={touched.wage && errors.wage ? true : false}
                              />

                              <CustomSelect
                                value={values.per}
                                labelName="Per"
                                stateName="per"
                                dropDownData={PER}
                                setFieldValue={setFieldValue}
                                setValue={() => {}}
                                margin="8px 0 0 0"
                                error={touched.per && errors.per ? true : false}
                              />
                            </SharedStyled.TwoInputDiv>

                            <SharedStyled.TwoInputDiv>
                              {hasPieceworkVersion && (
                                <InputWithValidation
                                  labelName="$ Piece Work Hourly Rate"
                                  stateName="pieceWorkHourlyRate"
                                  twoInput={true}
                                  error={touched.pieceWorkHourlyRate && errors.pieceWorkHourlyRate ? true : false}
                                />
                              )}

                              {hasPieceworkVersion && (
                                <CustomSelect
                                  value={values.versionId ?? ''}
                                  dropDownData={pwVersions?.map((itm) => itm?.name)}
                                  labelName="Piece Work Version"
                                  stateName="versionId"
                                  setFieldValue={setFieldValue}
                                  showInitialValue
                                  margin="8px 0 0 0"
                                  setValue={() => {}}
                                  error={touched.versionId && errors.versionId ? true : false}
                                />
                              )}
                            </SharedStyled.TwoInputDiv>
                            {hasSalesCommission && (
                              <>
                                <SharedStyled.TwoInputDiv>
                                  <InputWithValidation
                                    labelName="Sales Commission %"
                                    stateName="salesCommision"
                                    twoInput={true}
                                    onChange={(e: any) => {
                                      const userInput = e.target.value

                                      if (userInput === '%') {
                                        setFieldValue('salesCommision', '')
                                        return
                                      }
                                      let numValue = userInput.replace(/[%]/g, '')

                                      setFieldValue('salesCommision', `${numValue}%`)
                                    }}
                                    error={touched.salesCommision && errors.salesCommision ? true : false}
                                  />
                                  <InputWithValidation
                                    labelName="Self Gen Commission"
                                    stateName="selfLead"
                                    twoInput={true}
                                    onChange={(e: any) => {
                                      const userInput = e.target.value

                                      if (userInput === '%') {
                                        setFieldValue('selfLead', '')
                                        return
                                      }
                                      let numValue = userInput.replace(/[%]/g, '')

                                      setFieldValue('selfLead', `${numValue}%`)
                                    }}
                                    error={touched.selfLead && errors.selfLead ? true : false}
                                  />
                                </SharedStyled.TwoInputDiv>
                                {/* TODO: Hardcoded, benchmarks toggle will be removed in future  */}
                                {isProPlusPlan ? (
                                  <CheckDivContainer className="piecework">
                                    <Toggle
                                      title="Benchmark bonus"
                                      isToggled={values?.useBenchmarkBonus!}
                                      onToggle={() => {
                                        setFieldValue('useBenchmarkBonus', !values?.useBenchmarkBonus)
                                      }}
                                    />
                                  </CheckDivContainer>
                                ) : null}
                              </>
                            )}
                            {values?.position === 'Foreman' ? (
                              <>
                                <h4>Foreman Bonus:</h4>
                                <SharedStyled.TwoInputDiv>
                                  <InputWithValidation
                                    labelName="Self Bonus %"
                                    stateName="ownPieceWork"
                                    twoInput={true}
                                    forceType="number"
                                    error={touched.ownPieceWork && errors.ownPieceWork ? true : false}
                                  />

                                  <InputWithValidation
                                    labelName="Crew Bonus %"
                                    stateName="crewPieceWork"
                                    twoInput={true}
                                    forceType="number"
                                    error={touched.crewPieceWork && errors.crewPieceWork ? true : false}
                                  />
                                </SharedStyled.TwoInputDiv>
                              </>
                            ) : null}

                            {isInviteModal ? (
                              <>
                                <CustomSelect
                                  value={values.department!}
                                  labelName="Department"
                                  stateName="department"
                                  dropDownData={departmentsData?.department?.map((itm: any) => itm?.name)}
                                  setFieldValue={setFieldValue}
                                  margin="10px 0 0 0"
                                  setValue={() => {}}
                                  error={touched.department && errors.department ? true : false}
                                />
                                <CustomSelect
                                  value={values.manager!}
                                  labelName="Manager"
                                  stateName="manager"
                                  dropDownData={managerData?.map((itm) => itm?.name)}
                                  setFieldValue={setFieldValue}
                                  margin="10px 0 0 0"
                                  setValue={() => {}}
                                  error={touched.manager && errors.manager ? true : false}
                                  showInitialValue
                                />{' '}
                              </>
                            ) : null}
                            {isInviteModal ? null : (
                              <InputWithValidation
                                labelName="Reason for change"
                                stateName="reasonForChange"
                                error={touched.reasonForChange && errors.reasonForChange ? true : false}
                              />
                            )}
                          </>
                        )}

                        <SharedStyled.ButtonContainer marginTop="20px">
                          <Button type="submit" isLoading={loading}>
                            {isEdit ? 'Save' : isInviteModal ? 'Save' : 'Add'}
                          </Button>
                          <Button
                            type="button"
                            className={isInviteModal ? 'delete' : ''}
                            onClick={() => setCompensationModal(false)}
                          >
                            {isInviteModal ? 'Do this later' : 'Cancel'}
                          </Button>
                        </SharedStyled.ButtonContainer>
                      </SharedStyled.Content>
                    </Form>
                  </SharedStyled.SettingModalContentContainer>
                )}
              </>
            )
          }}
        </Formik>
      </Styled.CompensationContainer>
    </>
  )
}

export const getPayPeriods2NHR = (period, end1, payday1, end2, payday2) => {
  var today = new Date()
  today.setMonth(today.getMonth() + 2)
  var payPeriods = []
  // set end date to isodate and end of local day
  var end1 = new Date(end1 + 'T23:59:59.999')
  if (period === 'Every week' || period === 'Every other week') {
    if (period == 'Every week') {
      var increment = 7
    } else if (period == 'Every other week') {
      var increment = 14
    }
    var start1 = new Date(end1)
    start1.setDate(start1.getDate() - (increment - 1))
    start1.setHours(0, 0, 0, 0)
    var reportEnd = 0
    var i = 0
    while (reportEnd < today) {
      var startDate = new Date(start1)
      var endDate = new Date(end1)
      var payDate = new Date(payday1 + 'T00:00')
      var obj = {}
      obj.periodStart = new Date(startDate.setDate(startDate.getDate() + increment * i))
      obj.periodEnd = new Date(endDate.setDate(endDate.getDate() + increment * i))
      obj.periodPayday = new Date(payDate.setDate(payDate.getDate() + increment * i))
      payPeriods.push(obj)
      reportEnd = obj.periodEnd
      i++
    }
  } else if (period === 'Once per month') {
    var start1 = new Date(end1)
    start1.setDate(start1.getDate() + 1)
    var reportEnd = 0
    var i = 0
    while (reportEnd < today) {
      var startMonth = end1.getMonth()
      var subMonth = startMonth - 1
      var startDate = start1.getDate()
      // if pay period ends on last day of current month, set endDate to 31 to choose each last day
      if (daysInMonth(end1.getFullYear(), startMonth) === end1.getDate()) {
        var endDate = 31
      } else {
        var endDate = end1.getDate()
      }
      var payDate = Number(payday1)
      var payMonth = startMonth
      if (payDate <= endDate && payMonth === startMonth) {
        payMonth++
      }
      var obj = {}
      var thisMonthDays = daysInMonth(end1.getFullYear(), startMonth + i)
      //on this loop, if there are less days in the month than the endDate, set to days in month
      if (thisMonthDays < endDate) {
        endDate = thisMonthDays
      }
      //if
      if (thisMonthDays === endDate) {
        subMonth++
      }
      // if there are less days in month than the payDate, set payDate to days in month
      if (thisMonthDays < payDate) {
        payDate = thisMonthDays
      }
      obj.periodStart = new Date(end1.getFullYear(), subMonth + i, startDate, 0, 0, 0, 0)
      obj.periodEnd = new Date(end1.getFullYear(), startMonth + i, endDate, 23, 59, 59, 999)
      obj.periodPayday = new Date(end1.getFullYear(), payMonth + i, payDate, 0, 0, 0, 0)
      payPeriods.push(obj)
      reportEnd = obj.periodEnd
      i++
    }
  } else if (period === 'Twice per month') {
    // FIRST GET DATES OF THE MONTH
    payday1 = Number(payday1)
    end2 = new Date(end2 + 'T23:59:59.999')
    payday2 = Number(payday2)
    var payDate1 = payday1
    var payDate2 = payday2
    var endDate1 = end1.getDate()
    var endDate2 = end2.getDate()
    var startDate1 = new Date(end2)
    startDate1.setDate(startDate1.getDate() + 1)
    startDate1 = startDate1.getDate()
    var startDate2 = new Date(end1)
    startDate2.setDate(startDate2.getDate() + 1)
    startDate2 = startDate2.getDate()
    var startMonth = end1.getMonth() // Jan
    var startYear = end1.getFullYear()
    var reportEnd = 0

    // Create logic to include last 3 months
    var monthsBack = 3 // Go back 3 months
    var payPeriods = []
    var i = -monthsBack // Start from -3 (3 months ago)

    while (reportEnd < today || i <= 0) {
      var thisMonthDays = daysInMonth(startYear, startMonth + i)
      var nextMonthDays = daysInMonth(startYear, startMonth + 1 + i)

      // Create variables for the loop to prevent changing the presets
      var s1 = startDate1
      var s2 = startDate2
      var e1 = endDate1 + 1
      var e2 = endDate2
      var p1 = payDate1
      var p2 = payDate2
      var s1Month = startMonth
      var e1Month = startMonth + 1
      var p1Month = startMonth + 2

      var obj = {}
      obj.periodStart = new Date(startYear, s1Month + i, s1, 0, 0, 0, 0)
      if (thisMonthDays < e1) {
        e1 = thisMonthDays
      }
      if (e1 > s1) {
        e1Month--
        p1Month--
      }
      obj.periodEnd = new Date(startYear, e1Month + i, e1, 23, 59, 59, 999)
      if (thisMonthDays < p1) {
        p1 = thisMonthDays
      }
      if (payDate1 > endDate1) {
        p1Month--
      }

      obj.periodPayday = new Date(startYear, p1Month + i, p1, 0, 0, 0, 0)
      payPeriods.push(obj)

      var obj2 = {}
      if (nextMonthDays < s2) {
        s2 = nextMonthDays
      }
      s1Month++
      if (s2 < e1) {
        e1Month++
      }
      obj2.periodStart = new Date(startYear, s1Month + i, s2, 0, 0, 0, 0)
      if (nextMonthDays < e2) {
        e2 = nextMonthDays
      }
      obj2.periodEnd = new Date(startYear, e1Month + i, e2, 23, 59, 59, 999)
      if (nextMonthDays < p2) {
        p2 = nextMonthDays
      }
      if (payDate2 < endDate2) {
        p1Month++
      }
      obj2.periodPayday = new Date(startYear, p1Month + i, p2, 0, 0, 0, 0)

      payPeriods.push(obj2)
      reportEnd = obj2.periodEnd
      i++
    }
  }

  return payPeriods.reverse()
}
