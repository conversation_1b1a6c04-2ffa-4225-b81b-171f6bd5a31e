import { ErrorMessage } from 'formik'
import React, { useEffect, useRef, useState } from 'react'

import { DownArrowIcon } from '../../assets/icons/DownArrowIcon'
import { UpArrowIcon } from '../../assets/icons/UpArrowIcon'
import { getNameFrom_Id } from '../helpers/util'
import { useClickOutside } from '../hooks/useClickOutside'
import { NormalInput } from '../normalInput/NormalInput'
import * as Styled from './style'
import { SearchLoader } from '../components/loader/style'
import { getLeadSrcDropdownName } from '../../modules/leadSource/LeadSource'

interface I_AutoCompleteProps {
  addNewText?: string
  apiSearch?: boolean
  autoFillData?: any
  className?: string
  disabled?: boolean
  error?: boolean
  labelName: string
  onAddClick?: any
  onChange?: any
  options: Array<string>
  setFieldValue?: any
  setSearchTerm?: any
  setValueOnClick?: any
  showAddOption?: boolean
  stateName: string
  value?: string
  setTypeForAction?: any
  setDuration?: any
  refererres?: any
  leadSrcData?: any
  dropdownHeight?: string
  borderRadius?: string
  selectedValue?: string
  validate?: boolean
  preSelected?: boolean
  setClientAddress?: React.Dispatch<React.SetStateAction<string>>
  searchLoader?: boolean
  isCrewProjectReport?: boolean
  isWarranty?: boolean
}

const AutoComplete: React.FC<I_AutoCompleteProps> = (props) => {
  const {
    addNewText,
    apiSearch,
    autoFillData,
    className,
    disabled,
    error,
    labelName,
    onAddClick,
    onChange,
    options,
    setFieldValue,
    setSearchTerm,
    setValueOnClick,
    showAddOption,
    stateName,
    value,
    setTypeForAction,
    setDuration,
    refererres,
    leadSrcData,
    dropdownHeight,
    borderRadius,
    selectedValue,
    validate,
    setClientAddress,
    searchLoader,
    preSelected,
    isCrewProjectReport,
    isWarranty,
  } = props

  const [filterOptions, setFilterOptions] = useState(() => options)
  const [showDropdown, setShowDropdown] = useState<boolean>(false)
  const [onFocused, setOnFocused] = useState<boolean>(false)
  const [localSearchTerm, setLocalSearchTerm] = useState('')

  const ref = useRef(null)
  const ref1 = useRef(null)

  useClickOutside(ref, setShowDropdown)
  useClickOutside(ref1, setOnFocused)

  useEffect(() => {
    if (apiSearch || !value) setFilterOptions(options)
  }, [options])
  useEffect(() => {
    if (preSelected && !localSearchTerm) setFilterOptions(options)
  }, [options, localSearchTerm])

  const handleFilterOptions = (str: string) => {
    setFilterOptions((prev) => {
      if (str === '') return options
      let newOptions = options?.filter((item) => item.toLowerCase().includes(str.toLowerCase()))
      return newOptions
    })
  }
  const handleSelectOption = (name: string) => {
    if (name) {
      !preSelected && handleFilterOptions(name)
      if (setValueOnClick) setValueOnClick(name)
      if (autoFillData) {
        handleNameChange(name, false)
      }
      setShowDropdown((prev) => !prev)
      if (preSelected) {
        setLocalSearchTerm('')
      }
    }
  }
  const handleInputChange = (str: string) => {
    setShowDropdown(true)
    setFieldValue && setFieldValue(stateName, str)
    onChange && onChange(str)
    handleFilterOptions(str)
    if (setSearchTerm) {
      setSearchTerm(str)
    }
    if (str && preSelected) {
      setLocalSearchTerm(str)
    }
    if (setTypeForAction) {
      setTypeForAction(str)
    }
  }
  // useEffect(() => {
  //   if (autoFillData) {
  //     if (filterOptions.length === 1) {
  //       handleNameChange(filterOptions[0], false)
  //     } else {
  //       handleNameChange(filterOptions[0], true)
  //     }
  //   }
  // }, [filterOptions])
  const handleNameChange = (name: string, reset: boolean) => {
    const selectedName = name
    const selectedData = autoFillData.find((item: any) => item.name.trim() === selectedName)

    if (isWarranty) {
      setFieldValue('street', selectedData.street)
      setFieldValue('city', selectedData.city)
      setFieldValue('state', selectedData.state)
      setFieldValue('zip', selectedData.zip)
      setFieldValue('contactId', selectedData._id)
      if (selectedData.street && selectedData.city && selectedData.state && selectedData.zip) {
        setClientAddress?.(
          `${selectedData.street || ''}, ${selectedData.city || ''}, ${selectedData.state || ''} ${
            selectedData.zip || ''
          }, USA`
        )
      } else {
        setFieldValue('duration', 0)
      }
    } else {
      if (selectedData && !reset) {
        const leadSourceId = selectedData?.leadSourceId
        const campaignId = selectedData?.campaignId || null
        console.log({ leadSourceId, campaignId, selectedData, leadSrcData }, campaignId || leadSourceId, leadSrcData)
        setFieldValue('street', selectedData.street)
        setFieldValue('city', selectedData.city)
        setFieldValue('state', selectedData.state)
        setFieldValue('zip', selectedData.zip)
        setFieldValue('distance', selectedData.distance)
        setFieldValue('contactId', selectedData._id)
        setFieldValue('leadSourceName', getLeadSrcDropdownName(campaignId || leadSourceId, leadSrcData)?.sourceName)
        // setFieldValue('leadSourceName', getNameFrom_Id(selectedData.leadSourceName, leadSrcData))
        setFieldValue('referredBy', getNameFrom_Id(selectedData.referredBy, refererres))

        if (selectedData.street && selectedData.city && selectedData.state && selectedData.zip) {
          setClientAddress?.(
            `${selectedData.street || ''}, ${selectedData.city || ''}, ${selectedData.state || ''} ${
              selectedData.zip || ''
            }, USA`
          )
        } else {
          setFieldValue('duration', 0)
        }
        if (setDuration) setDuration(selectedData?.duration)
      } else {
        setFieldValue('street', '')
        setFieldValue('city', '')
        setFieldValue('state', '')
        setFieldValue('zip', '')
        setFieldValue('leadSourceName', '')
        setFieldValue('referredBy', '')
        setFieldValue('distance', 0)
        setFieldValue('contactId', '')
        if (setDuration) setDuration(0)
      }
    }
  }

  return (
    <Styled.DropDownOuterContainer
      ref={ref1}
      className={className}
      onBlur={() => {
        if (!filterOptions?.length && validate) {
          setFieldValue(stateName, selectedValue)
        }
      }}
    >
      <Styled.DropDownContainer
        marginTop="8px"
        onClick={
          disabled
            ? () => {}
            : () => {
                setShowDropdown((prev) => !prev)
                setOnFocused((prev) => !prev)
              }
        }
        ref={ref}
        focus={onFocused}
        error={error}
        disabled={disabled}
        onKeyDown={(e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            setOnFocused(false)
            setShowDropdown(false)
          }
        }}
      >
        {/* {value && <Styled.DropDownLabel>{labelName}</Styled.DropDownLabel>} */}

        <NormalInput
          padding={'20px 40px 8px 16px'}
          labelName={labelName}
          stateName={stateName}
          error={error}
          twoInput={true}
          value={value}
          noMessage
          disabled={disabled}
          selectTextOnFocus
          onChange={(e: any) => {
            handleInputChange(e.target.value)
          }}
        />

        {/* <Styled.DropDownContent active={value}>{value ? value : labelName}</Styled.DropDownContent> */}
        <Styled.DropdownIconDiv
          onClick={
            disabled
              ? () => {}
              : (e) => {
                  e.stopPropagation()
                  setShowDropdown((prev) => !prev)
                }
          }
        >
          {searchLoader ? <SearchLoader /> : showDropdown ? <UpArrowIcon /> : <DownArrowIcon />}
        </Styled.DropdownIconDiv>
        <Styled.DropDownContentContainer $visibility={showDropdown} height={dropdownHeight} borderRadius={borderRadius}>
          {filterOptions.length > 0 ? (
            filterOptions.map((data, index) => {
              if (isCrewProjectReport) {
                const [titleClient, start, done] = data?.split('|')?.map((part) => part?.trim())
                const [title, clientName] = titleClient?.split(':')?.map((part) => part?.trim())
                const startDate = start?.replace('start: ', '')
                const doneDate = done?.replace('done: ', '')

                return (
                  <Styled.DropDownItem
                    borderRadius={borderRadius}
                    key={index}
                    onClick={() => {
                      setFieldValue && setFieldValue(stateName, data)
                      onChange && onChange(data)
                      setShowDropdown(false)
                      handleSelectOption(data)
                    }}
                    active={value === data}
                  >
                    <div className="project-info">
                      <span>{title}: </span>
                      <span>{clientName}</span>
                      {startDate && (
                        <>
                          <span> | </span>
                          <span className="label">start: </span> <span className="date">{startDate}</span>{' '}
                        </>
                      )}
                      {doneDate && (
                        <>
                          <span> | </span>
                          <span className="label">done: </span> <span className="date">{doneDate}</span>{' '}
                        </>
                      )}
                    </div>
                  </Styled.DropDownItem>
                )
              }

              return (
                <Styled.DropDownItem
                  borderRadius={borderRadius}
                  key={index}
                  onClick={() => {
                    setFieldValue && setFieldValue(stateName, data)
                    onChange && onChange(data)
                    setShowDropdown(false)
                    handleSelectOption(data)
                  }}
                  active={value === data}
                >
                  {data}
                </Styled.DropDownItem>
              )
            })
          ) : !showAddOption ? (
            <Styled.DropDownItem noHover={true}>Nothing found</Styled.DropDownItem>
          ) : null}
          {showAddOption ? (
            <Styled.DropDownItem
              borderRadius={borderRadius}
              fontWeight={700}
              onClick={() => {
                onAddClick && onAddClick(value)
                setShowDropdown(false)
              }}
            >
              {addNewText ?? '+ Add New'}
            </Styled.DropDownItem>
          ) : null}
        </Styled.DropDownContentContainer>
        {/* <Styled.InputFieldNoDisplay
          name={stateName}
          onFocus={
            disabled
              ? () => {}
              : () => {
                  setShowDropdown(true)
                  setOnFocused(true)
                }
          }
          onBlur={
            disabled
              ? () => {}
              : () => {
                  setShowDropdown(false)
                  setOnFocused(false)
                }
          }
          disabled={disabled}
          type="checkbox"
        /> */}
      </Styled.DropDownContainer>
      {error && (
        <Styled.ErrorMsg width="100%">
          {/* @ts-ignore */}
          <ErrorMessage component="div" name={stateName} />
        </Styled.ErrorMsg>
      )}
    </Styled.DropDownOuterContainer>
  )
}

export default AutoComplete
