import { Field } from 'formik'
import styled from 'styled-components'
import { colors } from '../../styles/theme'
import { Nue } from '../helpers/constants'

export const DropDownContentContainer = styled.div<{ $visibility?: boolean; height?: string; borderRadius?: string }>`
  display: ${(props) => (props.$visibility ? 'block' : 'none')};
  position: absolute;
  border: 1px solid ${colors.darkGrey};
  width: 100%;
  max-height: ${(props) => (props.height ? props.height : 'fit-content')};
  top: 68px;
  left: 0px;
  /* border-radius: ${(props) => (props.borderRadius ? props.borderRadius : '8px')}; */
  overflow-y: auto;
  background: ${colors.white};
  z-index: 2;

  // Force hardware acceleration
  transform: translateZ(0);
  will-change: transform;
`

export const DropDownOuterContainer = styled.div<{ height?: string }>`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  &.clientHeight {
    ${DropDownContentContainer} {
      max-height: 300px;
    }
  }

  &.reverse {
    ${DropDownContentContainer} {
      top: inherit;
      bottom: 68px;
    }
  }
`

export const DropDownContainer = styled.div<any>`
  position: relative;
  width: 100%;
`

export const DropdownIconDiv = styled.div`
  cursor: pointer;
  position: absolute;
  top: 42%;
  right: 10px;
  svg {
    width: 20px;
    height: 20px;
    path {
      stroke: ${colors.darkGrey};
    }
  }
  span {
    position: static;
  }
`

export const DropDownLabel = styled.label`
  color: ${colors.darkGrey};
  position: absolute;
  font-size: 12px;
  top: 5px;
  left: 17px;
`

export const DropDownContent = styled.div<any>`
  font-family: ${(props) => (props.active ? Nue.medium : ``)};
  color: ${colors.darkGrey};
  position: absolute;
  font-size: 16px;
  font-weight: 500;
  top: ${(props) => (props.active ? `20px` : `15px`)};
  left: 17px;
`

export const DropDownItem = styled.div<any>`
  font-size: 14px;
  padding: 6px;
  font-weight: ${(props) => props.fontWeight};
  border-radius: ${(props) => (props.borderRadius ? props.borderRadius : '8px')};
  width: 100%;
  background: ${(props) => (props.active ? `${colors.darkGrey}` : `${colors.white}`)};
  color: ${(props) => (props.active ? `${colors.white}` : `${colors.darkGrey}`)};
  :hover {
    background: ${(props) => (props.noHover ? '' : `${colors.darkGrey}`)};
    color: ${(props) => (props.noHover ? '' : ` ${colors.white}`)};
  }
  /* text-transform: capitalize; */

  .project-info {
    .label {
      color: #888;
    }
  }
`

export const ErrorMsg = styled.div<any>`
  color: ${colors.errorRed};
  font-size: 10px;
  padding-top: 5px;
  padding-bottom: 5px;
  transition: all 0.3s ease;
  width: ${(props) => props.width};
  display: flex;
  justify-content: flex-start;
`

export const InputFieldNoDisplay = styled(Field)<any>`
  opacity: 0;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  cursor: pointer;
  z-index: -2;
`

export const AutoCompleteContainer = styled.div`
  position: relative;
`
