import React from 'react'

interface MergeContactsIconProps {
  color?: string
  width?: string
  height?: string
}

const MergeContacts: React.FC<MergeContactsIconProps> = ({ color = '#000000', width = '20', height = '20' }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 24 24" fill="none">
      <path
        d="M16.4693 7.47001L11.442 12.5039L9.52854 10.6033L8.47146 11.6675L11.4462 14.6223L17.5307 8.52999L16.4693 7.47001Z"
        fill="#1F2328"
      />
      <path fill-rule="evenodd" clip-rule="evenodd" d="M5 3V19H21V3H5ZM19.5 4.5H6.5V17.5H19.5V4.5Z" fill="#1F2328" />
      <path d="M2 6H3.5V20.5H18V22H2V6Z" fill="#1F2328" />
    </svg>
  )
}

export default MergeContacts
