import * as Styled from './style'
import * as SharedStyled from '../../styles/styled'
import { Column, usePagination, useTable, useAsyncDebounce, useGlobalFilter, useFilters } from 'react-table'
import { forwardRef, Fragment, useEffect, useRef, useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { colors } from '../../styles/theme'
import Button from '../components/button/Button'
import useDebounce from '../hooks/useDebounce'
import { dayjsFormat, getEnumValue } from '../helpers/util'

interface I_Table {
  activeItem?: string
  client?: boolean
  columns: any
  crew?: boolean
  invite?: boolean
  data: any
  defaultColumn?: any
  fetchData: ({ pageSize, pageIndex }: any) => void
  filterLoading?: boolean
  isLoadMoreLoading?: boolean
  loading?: boolean
  member?: boolean
  minWidth?: string
  noBorder?: boolean
  noLink?: boolean
  noOverflow?: boolean
  noPagination?: boolean
  noSearch?: boolean
  onRowClick?: (val: any) => void
  padding?: string
  pageCount?: number
  pageSizes?: number
  paySchedule?: boolean
  searchPlaceholder?: string
  setLastLimit?: React.Dispatch<React.SetStateAction<number>>
  updateMyData?: any
  nestedRow?: boolean
  hideHeader?: boolean
  clickedIds?: string[]
  referrerCount?: any
}

export const Table = forwardRef((props: I_Table, ref: any) => {
  const {
    activeItem = '',
    client,
    columns,
    crew,
    data,
    defaultColumn,
    fetchData,
    isLoadMoreLoading,
    loading,
    member,
    minWidth,
    noBorder,
    noLink,
    noOverflow,
    noPagination,
    noSearch,
    hideHeader,
    onRowClick,
    padding,
    pageCount: controlledPageCount,
    pageSizes,
    paySchedule,
    searchPlaceholder,
    setLastLimit,
    updateMyData,
    nestedRow,
    clickedIds,
    referrerCount,
    filterLoading,
    invite,
  } = props

  const navigate = useNavigate()
  const location = useLocation()
  const [first, setfirst] = useState(0)
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    pageOptions,
    pageCount,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    visibleColumns,
    preGlobalFilteredRows,
    setGlobalFilter,
    // Get the state from the instance
    state: { pageIndex, pageSize, globalFilter },
  }: any = useTable(
    {
      columns,
      data,
      defaultColumn,
      // initialState: { pageIndex: first, pageSize: 10 }, // Pass our hoisted table state
      initialState: { pageIndex: first, pageSize: pageSizes ? pageSizes : 10 }, // Pass our hoisted table state
      manualPagination: true, // Tell the usePagination
      // hook that we'll handle our own data fetching
      // This means we'll also have to provide our own
      // pageCount.
      pageCount: controlledPageCount,
      updateMyData,
    },
    useFilters,
    useGlobalFilter,
    usePagination
  )

  const [value, setValue] = useState(globalFilter)
  const debouncedValue = useDebounce(value, 500)

  // const [isLoading, setisLoading] = useState(loading)
  const [limit, setLimit] = useState(20)

  const onSearchChange = useAsyncDebounce((value: any) => {
    setGlobalFilter(value || undefined)
  }, 200)

  useEffect(() => {
    fetchData({ pageIndex, pageSize: limit, search: debouncedValue?.trim() || undefined })
    setLastLimit?.(limit)
  }, [fetchData, pageIndex, pageSize, debouncedValue, first, limit])

  // useEffect(() => {
  //   const id = setTimeout(() => {
  //     setisLoading(false)
  //   }, 3000)

  //   return () => {
  //     clearTimeout(id)
  //   }
  // }, [])
  useEffect(() => {
    if (ref?.current) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            const intersecting = entry.isIntersecting
            if (intersecting) {
              const button = document.getElementById('load')
              button?.click()
            }
          })
        },
        {
          threshold: 1,
        }
      )

      observer.observe(ref.current)
    }
  }, [ref?.current])

  return (
    <Styled.TableOuterContainer noOverflow={noOverflow}>
      {noSearch ? (
        <></>
      ) : (
        <>
          <Styled.SearchContainer>
            <Styled.SearchInput
              type="search"
              placeholder="Search..."
              value={value || ''}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setValue(e.target.value)
                onSearchChange(e.target.value)
              }}
            />
            {/* <GlobalFilter
          preGlobalFilteredRows={preGlobalFilteredRows}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
        /> */}
            {/*      {rows?.length ? (
              <Styled.ShowEntriesDiv>
                <Styled.ShowText>Show</Styled.ShowText>
                <Styled.SelectDiv
                  value={pageSize}
                  onChange={(e) => {
                    setPageSize(Number(e.target.value))
                  }}
                >
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <Styled.SelectOption key={pageSize} value={pageSize}>
                      {pageSize}
                    </Styled.SelectOption>
                  ))}
                </Styled.SelectDiv>
                <Styled.ShowText>entries</Styled.ShowText>
              </Styled.ShowEntriesDiv>
            ) : null}*/}
          </Styled.SearchContainer>
        </>
      )}
      <Styled.TableContainer {...getTableProps()} edit={updateMyData} noLink={noLink} minWidth={minWidth}>
        {hideHeader ? null : (
          <Styled.TableHeader>
            {headerGroups.map((headerGroup: any, idx: number) => (
              <Styled.TableRow {...headerGroup.getHeaderGroupProps()} noLink={true} key={idx}>
                {headerGroup.headers.map((column: any, index: number) => (
                  <Styled.TableHeading noBorder={noBorder} padding={padding} {...column.getHeaderProps()} key={index}>
                    {column.render('Header')}
                  </Styled.TableHeading>
                ))}
              </Styled.TableRow>
            ))}
          </Styled.TableHeader>
        )}

        <Styled.TableBody {...getTableBodyProps()} filterLoading={data?.length && filterLoading}>
          {rows.length > 0 ? (
            rows.map((row: any, index: number) => {
              prepareRow(row)
              return (
                <Fragment key={index}>
                  <Styled.TableRow
                    noBorder={noBorder}
                    key={index}
                    // type={index % 2 === 0 ? 'even' : 'odd'}
                    noLink={noLink ? noLink : crew || invite ? (row.original.link ? false : true) : ''}
                    className={
                      activeItem === row?.original?.orderId
                        ? 'active'
                        : row?.original?.active === false
                        ? 'inactive'
                        : ''
                    }
                    {...row.getRowProps()}
                    onClick={() => {
                      if (onRowClick) {
                        onRowClick(row.original)
                        return
                      }
                      if (member) {
                        navigate(`/team/member/${row.original.id}/${row.original.memberId}/${row.original.managerId}`)
                      }
                      if (paySchedule) {
                        navigate(`/settings/pay-schedule/edit/${row.original.payScheduleId}`)
                      }
                      if (crew) {
                        row.original.link
                          ? navigate(`/team/crew/member/${row.original.managerId}/${row.original.crewId}`)
                          : ''
                      }
                      // if (client) {
                      //   navigate(`/client/profile/${row.original.clientId}/${row.original.isDeleted}`)
                      // }
                    }}
                    color={crew && row.original.retired ? colors.grey : ''}
                  >
                    {row.cells.map((cell: any, idx: number) => {
                      return (
                        <Styled.TableData
                          noBorder={noBorder}
                          padding={row.original.padding && idx === 0 ? row.original.padding : padding}
                          {...cell.getCellProps()}
                          key={idx}
                        >
                          {client ? (
                            <Link to={`/contact/profile/${row.original.clientId}/${row.original.isDeleted}`}>
                              {cell.render('Cell')}
                            </Link>
                          ) : (
                            <>{cell.render('Cell')}</>
                          )}
                        </Styled.TableData>
                      )
                    })}
                  </Styled.TableRow>
                  {nestedRow &&
                    clickedIds?.includes(row?.original?._id) &&
                    referrerCount?.[row?.original?._id]?.opportunities?.map((v: any) => [
                      <Styled.TableRow
                        onClick={(event: any) => {
                          {
                            event.stopPropagation()
                            navigate(`/${getEnumValue(v?.stageGroup)}/opportunity/${v?._id}`)
                          }
                        }}
                      >
                        <Styled.TableData color="grey">
                          &emsp;&emsp;{v?.PO}-{v?.num}
                        </Styled.TableData>
                        <Styled.TableData color="grey">{dayjsFormat(v?.newLeadDate, 'M/D/YY')}</Styled.TableData>
                      </Styled.TableRow>,
                    ])}
                  {/* clickedIds.map(id => response[id]?.name).filter(Boolean); */}
                </Fragment>
              )
            })
          ) : globalFilter ? (
            <Styled.LoaderContainer>
              <td>
                <Styled.LoaderContent>No Results</Styled.LoaderContent>
              </td>
            </Styled.LoaderContainer>
          ) : loading ? (
            [1, 2, 3, 4, 5, 6].map((idx: number) => (
              <Styled.TableRow key={idx} noLink>
                {headerGroups[0].headers.map((headerGroup: any, key: number) => (
                  <Styled.TableData key={key}>
                    <SharedStyled.SkeletonLoader key={key}>
                      <div className="skeleton"></div>
                    </SharedStyled.SkeletonLoader>
                  </Styled.TableData>
                ))}
              </Styled.TableRow>
            ))
          ) : (
            <Styled.LoaderContainer>
              <td>
                <Styled.LoaderContent>No Results</Styled.LoaderContent>
              </td>
            </Styled.LoaderContainer>
          )}
        </Styled.TableBody>
      </Styled.TableContainer>
      {noPagination ? (
        <></>
      ) : (
        <>
          {rows?.length ? (
            <>
              {(!(data.length < limit) || isLoadMoreLoading) && (
                <SharedStyled.FlexRow ref={ref} justifyContent="center" margin="10px 0" className="loaderCont">
                  <SharedStyled.Loader
                    onClick={() => {
                      // nextPage()
                      setLimit(data?.length + 20)
                    }}
                    id="load"
                    color={colors.darkGrey}
                    width="22px"
                    height="22px"
                  />
                </SharedStyled.FlexRow>
              )}
              {/*  <Styled.Pagination minWidth={minWidth}>
                <Styled.PaginationInfoDiv>
                  <Styled.PageSpan>
                    Page{' '}
                    <Styled.PageStrong>
                      {pageIndex + 1} of {pageOptions.length}
                    </Styled.PageStrong>{' '}
                  </Styled.PageSpan>
                  /~ <Styled.PageSpan>
            | Go to page:{' '}
            <Styled.InputNumber
              type="number"
              defaultValue={pageIndex + 1}
              onChange={(e) => {
                const page = e.target.value ? Number(e.target.value) - 1 : 0
                gotoPage(page)
              }}
              style={{ width: '100px' }}
            />
          </Styled.PageSpan>{' '} ~/
                </Styled.PaginationInfoDiv>
                <Styled.PaginationNavigationDiv>
                  <Styled.IconContainer
                    onClick={() => {
                      gotoPage(0)
                      setfirst(0)
                    }}
                    disabled={!canPreviousPage}
                  >
                    {'<<'}
                  </Styled.IconContainer>{' '}
                  <Styled.IconContainer
                    onClick={() => {
                      previousPage()
                      setfirst(first - 1)
                    }}
                    disabled={!canPreviousPage}
                  >
                    {'<'}
                  </Styled.IconContainer>{' '}
                  <Styled.IconContainer
                    onClick={() => {
                      nextPage()
                      setfirst(first + 1)
                    }}
                    disabled={!canNextPage}
                  >
                    {'>'}
                  </Styled.IconContainer>{' '}
                  <Styled.IconContainer
                    onClick={() => {
                      gotoPage(pageCount - 1)
                      setfirst(pageCount - 1)
                    }}
                    disabled={!canNextPage}
                  >
                    {'>>'}
                  </Styled.IconContainer>{' '}
                </Styled.PaginationNavigationDiv>
              </Styled.Pagination>*/}
            </>
          ) : null}
        </>
      )}
    </Styled.TableOuterContainer>
  )
})
